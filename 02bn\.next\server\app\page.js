/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin.NEIL%5CDesktop%5C02bn%5C02bn%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin.NEIL%5CDesktop%5C02bn%5C02bn&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin.NEIL%5CDesktop%5C02bn%5C02bn%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin.NEIL%5CDesktop%5C02bn%5C02bn&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin.NEIL%5CDesktop%5C02bn%5C02bn%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin.NEIL%5CDesktop%5C02bn%5C02bn&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-montserrat%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22montserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-montserrat%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22montserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkbWluLk5FSUwlNUMlNUNEZXNrdG9wJTVDJTVDMDJibiU1QyU1QzAyYm4lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQW1HIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pbi5ORUlMXFxcXERlc2t0b3BcXFxcMDJiblxcXFwwMmJuXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW4uTkVJTFxcRGVza3RvcFxcMDJiblxcMDJiblxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f49c9563831c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluLk5FSUxcXERlc2t0b3BcXDAyYm5cXDAyYm5cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY0OWM5NTYzODMxY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_variable_font_montserrat_display_swap_variableName_montserrat___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\",\"display\":\"swap\"}],\"variableName\":\"montserrat\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Montserrat\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-montserrat\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"montserrat\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_variable_font_montserrat_display_swap_variableName_montserrat___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_variable_font_montserrat_display_swap_variableName_montserrat___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"02Bn - Zero to Billion\",\n    description: \"Comprehensive personal growth and wealth tracking dashboard\",\n    keywords: [\n        \"wealth tracking\",\n        \"personal growth\",\n        \"dashboard\",\n        \"goals\",\n        \"productivity\"\n    ],\n    authors: [\n        {\n            name: \"02Bn Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#00d4ff\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_variable_font_montserrat_display_swap_variableName_montserrat___WEBPACK_IMPORTED_MODULE_3___default().variable)} font-sans antialiased min-h-screen bg-background text-foreground`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative min-h-screen command-center\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-montserrat%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22montserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-montserrat%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22montserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkbWluLk5FSUwlNUMlNUNEZXNrdG9wJTVDJTVDMDJibiU1QyU1QzAyYm4lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQW1HIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pbi5ORUlMXFxcXERlc2t0b3BcXFxcMDJiblxcXFwwMmJuXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdmin.NEIL%5C%5CDesktop%5C%5C02bn%5C%5C02bn%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/KPICard */ \"(ssr)/./src/components/ui/KPICard.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ProgressBar */ \"(ssr)/./src/components/ui/ProgressBar.tsx\");\n/* harmony import */ var _components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/StatCard */ \"(ssr)/./src/components/ui/StatCard.tsx\");\n/* harmony import */ var _components_charts_LineChart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/charts/LineChart */ \"(ssr)/./src/components/charts/LineChart.tsx\");\n/* harmony import */ var _components_charts_AreaChart__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/charts/AreaChart */ \"(ssr)/./src/components/charts/AreaChart.tsx\");\n/* harmony import */ var _components_ui_HeatMap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/HeatMap */ \"(ssr)/./src/components/ui/HeatMap.tsx\");\n/* harmony import */ var _components_ui_FloatingActionButton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/FloatingActionButton */ \"(ssr)/./src/components/ui/FloatingActionButton.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n// Mock data - in a real app, this would come from an API\nconst mockData = {\n    netWorth: 125000,\n    monthlyIncome: 8500,\n    goalProgress: 68,\n    habitsCompleted: 12,\n    totalHabits: 18,\n    weeklyGoals: 4,\n    completedGoals: 3,\n    productivityScore: 85\n};\n// Mock chart data\nconst wealthData = [\n    {\n        name: \"Jan\",\n        value: 95000\n    },\n    {\n        name: \"Feb\",\n        value: 102000\n    },\n    {\n        name: \"Mar\",\n        value: 108000\n    },\n    {\n        name: \"Apr\",\n        value: 115000\n    },\n    {\n        name: \"May\",\n        value: 120000\n    },\n    {\n        name: \"Jun\",\n        value: 125000\n    }\n];\nconst incomeData = [\n    {\n        name: \"Jan\",\n        value: 7200\n    },\n    {\n        name: \"Feb\",\n        value: 7800\n    },\n    {\n        name: \"Mar\",\n        value: 8100\n    },\n    {\n        name: \"Apr\",\n        value: 8300\n    },\n    {\n        name: \"May\",\n        value: 8200\n    },\n    {\n        name: \"Jun\",\n        value: 8500\n    }\n];\n// Mock habit tracking data\nconst habitData = Array.from({\n    length: 365\n}, (_, i)=>{\n    const date = new Date();\n    date.setDate(date.getDate() - (365 - i));\n    return {\n        date: date.toISOString().split('T')[0],\n        value: Math.floor(Math.random() * 10),\n        level: Math.floor(Math.random() * 5)\n    };\n});\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold font-display bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 bg-clip-text text-transparent mb-2\",\n                        children: \"Welcome to Mission Control\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground text-lg\",\n                        children: \"Track your journey from zero to billion with precision and purpose.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Net Worth\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(mockData.netWorth),\n                        change: 12.5,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                        color: \"primary\",\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Monthly Income\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(mockData.monthlyIncome),\n                        change: 8.2,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                        color: \"secondary\",\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Goal Progress\",\n                        value: `${mockData.goalProgress}%`,\n                        change: 5.1,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                        color: \"accent\",\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Productivity Score\",\n                        value: mockData.productivityScore,\n                        change: -2.3,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                        color: \"primary\",\n                        trend: \"down\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"p-8\",\n                                glow: \"blue\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold font-display\",\n                                                children: \"Trajectory Hub\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-6 h-6 text-primary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Wealth Building Progress\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Target: $1M\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: mockData.netWorth,\n                                                        max: 1000000,\n                                                        color: \"primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Annual Goals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    mockData.completedGoals,\n                                                                    \"/\",\n                                                                    mockData.weeklyGoals * 13,\n                                                                    \" completed\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: mockData.completedGoals,\n                                                        max: mockData.weeklyGoals * 13,\n                                                        color: \"secondary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Daily Habits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    mockData.habitsCompleted,\n                                                                    \"/\",\n                                                                    mockData.totalHabits,\n                                                                    \" today\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: mockData.habitsCompleted,\n                                                        max: mockData.totalHabits,\n                                                        color: \"accent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Weekly Goals\",\n                                        value: mockData.weeklyGoals,\n                                        subtitle: \"This week\",\n                                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                        color: \"secondary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Achievements\",\n                                        value: \"23\",\n                                        subtitle: \"Total earned\",\n                                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        color: \"accent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Success Rate\",\n                                        value: \"94%\",\n                                        subtitle: \"Last 30 days\",\n                                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                        color: \"primary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        glow: \"blue\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-primary-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Net Worth Growth\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_AreaChart__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                data: wealthData,\n                                                color: \"primary\",\n                                                height: 250\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        glow: \"magenta\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-secondary-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Monthly Income\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_LineChart__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                data: incomeData,\n                                                color: \"secondary\",\n                                                height: 250\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                glow: \"magenta\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-secondary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recent Activity\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-primary-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Completed morning routine\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-auto\",\n                                                        children: \"2h ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-secondary-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Updated investment portfolio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-auto\",\n                                                        children: \"4h ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-accent-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Reached daily step goal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-auto\",\n                                                        children: \"6h ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                glow: \"gold\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-accent-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Next Milestones\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"$150K Net Worth\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"2 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Launch Side Business\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"6 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"First $10K Month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"8 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                glow: \"blue\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-primary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Habit Streak\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Current Streak\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-primary-500\",\n                                                        children: \"12 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_HeatMap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                data: habitData,\n                                                color: \"primary\",\n                                                weeks: 12,\n                                                cellSize: 10,\n                                                className: \"mt-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    glow: \"gold\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold font-display flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-6 h-6 mr-2 text-accent-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Today's Focus\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: new Date().toLocaleDateString('en-US', {\n                                        weekday: 'long',\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-primary-500\",\n                                            children: \"Priority Tasks\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Review investment portfolio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Complete morning workout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Update financial spreadsheet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-secondary-500\",\n                                            children: \"Learning Goals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Read 30 minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Complete online course module\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Practice new skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-accent-500\",\n                                            children: \"Health & Wellness\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Drink 8 glasses of water\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Meditate for 10 minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Take vitamins\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FloatingActionButton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                color: \"primary\",\n                tooltip: \"Quick Add\",\n                onClick: ()=>console.log(\"Quick add clicked\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/charts/AreaChart.tsx":
/*!*********************************************!*\
  !*** ./src/components/charts/AreaChart.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AreaChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AreaChart({ data, className, color = \"primary\", height = 300, showGrid = true, animated = true }) {\n    const colorMap = {\n        primary: {\n            stroke: \"#00d4ff\",\n            fill: \"rgba(0, 212, 255, 0.2)\"\n        },\n        secondary: {\n            stroke: \"#ff006b\",\n            fill: \"rgba(255, 0, 107, 0.2)\"\n        },\n        accent: {\n            stroke: \"#ffd700\",\n            fill: \"rgba(255, 215, 0, 0.2)\"\n        }\n    };\n    const CustomTooltip = ({ active, payload, label })=>{\n        if (active && payload && payload.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-dark p-3 rounded-lg border border-border/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        style: {\n                            color: colorMap[color].stroke\n                        },\n                        children: [\n                            \"Value: \",\n                            payload[0].value.toLocaleString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: animated ? {\n            opacity: 0,\n            y: 20\n        } : {\n            opacity: 1\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-full\", className),\n        style: {\n            height\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.AreaChart, {\n                data: data,\n                margin: {\n                    top: 5,\n                    right: 30,\n                    left: 20,\n                    bottom: 5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                            id: `gradient-${color}`,\n                            x1: \"0\",\n                            y1: \"0\",\n                            x2: \"0\",\n                            y2: \"1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"5%\",\n                                    stopColor: colorMap[color].stroke,\n                                    stopOpacity: 0.3\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"95%\",\n                                    stopColor: colorMap[color].stroke,\n                                    stopOpacity: 0\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    showGrid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.CartesianGrid, {\n                        strokeDasharray: \"3 3\",\n                        stroke: \"rgba(255, 255, 255, 0.1)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.XAxis, {\n                        dataKey: \"name\",\n                        axisLine: false,\n                        tickLine: false,\n                        tick: {\n                            fill: \"#9ca3af\",\n                            fontSize: 12\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.YAxis, {\n                        axisLine: false,\n                        tickLine: false,\n                        tick: {\n                            fill: \"#9ca3af\",\n                            fontSize: 12\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Tooltip, {\n                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 29\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.Area, {\n                        type: \"monotone\",\n                        dataKey: \"value\",\n                        stroke: colorMap[color].stroke,\n                        strokeWidth: 2,\n                        fill: `url(#gradient-${color})`,\n                        animationDuration: animated ? 1000 : 0\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\AreaChart.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jaGFydHMvQXJlYUNoYXJ0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRXVDO0FBU3JCO0FBQ2U7QUFpQmxCLFNBQVNDLFVBQVUsRUFDaENTLElBQUksRUFDSkMsU0FBUyxFQUNUQyxRQUFRLFNBQVMsRUFDakJDLFNBQVMsR0FBRyxFQUNaQyxXQUFXLElBQUksRUFDZkMsV0FBVyxJQUFJLEVBQ0E7SUFDZixNQUFNQyxXQUFXO1FBQ2ZDLFNBQVM7WUFBRUMsUUFBUTtZQUFXQyxNQUFNO1FBQXlCO1FBQzdEQyxXQUFXO1lBQUVGLFFBQVE7WUFBV0MsTUFBTTtRQUF5QjtRQUMvREUsUUFBUTtZQUFFSCxRQUFRO1lBQVdDLE1BQU07UUFBeUI7SUFDOUQ7SUFFQSxNQUFNRyxnQkFBZ0IsQ0FBQyxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsS0FBSyxFQUFPO1FBQ3BELElBQUlGLFVBQVVDLFdBQVdBLFFBQVFFLE1BQU0sRUFBRTtZQUN2QyxxQkFDRSw4REFBQ0M7Z0JBQUloQixXQUFVOztrQ0FDYiw4REFBQ2lCO3dCQUFFakIsV0FBVTtrQ0FBdUJjOzs7Ozs7a0NBQ3BDLDhEQUFDRzt3QkFBRWpCLFdBQVU7d0JBQVVrQixPQUFPOzRCQUFFakIsT0FBT0ksUUFBUSxDQUFDSixNQUFNLENBQUNNLE1BQU07d0JBQUM7OzRCQUFHOzRCQUN2RE0sT0FBTyxDQUFDLEVBQUUsQ0FBQ00sS0FBSyxDQUFDQyxjQUFjOzs7Ozs7Ozs7Ozs7O1FBSS9DO1FBQ0EsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUMvQixpREFBTUEsQ0FBQzJCLEdBQUc7UUFDVEssU0FBU2pCLFdBQVc7WUFBRWtCLFNBQVM7WUFBR0MsR0FBRztRQUFHLElBQUk7WUFBRUQsU0FBUztRQUFFO1FBQ3pERSxTQUFTO1lBQUVGLFNBQVM7WUFBR0MsR0FBRztRQUFFO1FBQzVCRSxZQUFZO1lBQUVDLFVBQVU7UUFBSTtRQUM1QjFCLFdBQVdGLDhDQUFFQSxDQUFDLFVBQVVFO1FBQ3hCa0IsT0FBTztZQUFFaEI7UUFBTztrQkFFaEIsNEVBQUNMLHFKQUFtQkE7WUFBQzhCLE9BQU07WUFBT3pCLFFBQU87c0JBQ3ZDLDRFQUFDWCwySUFBaUJBO2dCQUFDUSxNQUFNQTtnQkFBTTZCLFFBQVE7b0JBQUVDLEtBQUs7b0JBQUdDLE9BQU87b0JBQUlDLE1BQU07b0JBQUlDLFFBQVE7Z0JBQUU7O2tDQUM5RSw4REFBQ0M7a0NBQ0MsNEVBQUNDOzRCQUFlQyxJQUFJLENBQUMsU0FBUyxFQUFFbEMsT0FBTzs0QkFBRW1DLElBQUc7NEJBQUlDLElBQUc7NEJBQUlDLElBQUc7NEJBQUlDLElBQUc7OzhDQUMvRCw4REFBQ0M7b0NBQUtDLFFBQU87b0NBQUtDLFdBQVdyQyxRQUFRLENBQUNKLE1BQU0sQ0FBQ00sTUFBTTtvQ0FBRW9DLGFBQWE7Ozs7Ozs4Q0FDbEUsOERBQUNIO29DQUFLQyxRQUFPO29DQUFNQyxXQUFXckMsUUFBUSxDQUFDSixNQUFNLENBQUNNLE1BQU07b0NBQUVvQyxhQUFhOzs7Ozs7Ozs7Ozs7Ozs7OztvQkFHdEV4QywwQkFDQyw4REFBQ1IsK0lBQWFBO3dCQUNaaUQsaUJBQWdCO3dCQUNoQnJDLFFBQU87Ozs7OztrQ0FHWCw4REFBQ2QsdUlBQUtBO3dCQUNKb0QsU0FBUTt3QkFDUkMsVUFBVTt3QkFDVkMsVUFBVTt3QkFDVkMsTUFBTTs0QkFBRXhDLE1BQU07NEJBQVd5QyxVQUFVO3dCQUFHOzs7Ozs7a0NBRXhDLDhEQUFDdkQsdUlBQUtBO3dCQUNKb0QsVUFBVTt3QkFDVkMsVUFBVTt3QkFDVkMsTUFBTTs0QkFBRXhDLE1BQU07NEJBQVd5QyxVQUFVO3dCQUFHOzs7Ozs7a0NBRXhDLDhEQUFDckQseUlBQU9BO3dCQUFDc0QsdUJBQVMsOERBQUN2Qzs7Ozs7Ozs7OztrQ0FDbkIsOERBQUNuQixzSUFBSUE7d0JBQ0gyRCxNQUFLO3dCQUNMTixTQUFRO3dCQUNSdEMsUUFBUUYsUUFBUSxDQUFDSixNQUFNLENBQUNNLE1BQU07d0JBQzlCNkMsYUFBYTt3QkFDYjVDLE1BQU0sQ0FBQyxjQUFjLEVBQUVQLE1BQU0sQ0FBQyxDQUFDO3dCQUMvQm9ELG1CQUFtQmpELFdBQVcsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1qRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pbi5ORUlMXFxEZXNrdG9wXFwwMmJuXFwwMmJuXFxzcmNcXGNvbXBvbmVudHNcXGNoYXJ0c1xcQXJlYUNoYXJ0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSBcImZyYW1lci1tb3Rpb25cIjtcbmltcG9ydCB7XG4gIEFyZWFDaGFydCBhcyBSZWNoYXJ0c0FyZWFDaGFydCxcbiAgQXJlYSxcbiAgWEF4aXMsXG4gIFlBeGlzLFxuICBDYXJ0ZXNpYW5HcmlkLFxuICBUb29sdGlwLFxuICBSZXNwb25zaXZlQ29udGFpbmVyLFxufSBmcm9tIFwicmVjaGFydHNcIjtcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XG5cbmludGVyZmFjZSBEYXRhUG9pbnQge1xuICBuYW1lOiBzdHJpbmc7XG4gIHZhbHVlOiBudW1iZXI7XG4gIFtrZXk6IHN0cmluZ106IHN0cmluZyB8IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIEFyZWFDaGFydFByb3BzIHtcbiAgZGF0YTogRGF0YVBvaW50W107XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgY29sb3I/OiBcInByaW1hcnlcIiB8IFwic2Vjb25kYXJ5XCIgfCBcImFjY2VudFwiO1xuICBoZWlnaHQ/OiBudW1iZXI7XG4gIHNob3dHcmlkPzogYm9vbGVhbjtcbiAgYW5pbWF0ZWQ/OiBib29sZWFuO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcmVhQ2hhcnQoe1xuICBkYXRhLFxuICBjbGFzc05hbWUsXG4gIGNvbG9yID0gXCJwcmltYXJ5XCIsXG4gIGhlaWdodCA9IDMwMCxcbiAgc2hvd0dyaWQgPSB0cnVlLFxuICBhbmltYXRlZCA9IHRydWVcbn06IEFyZWFDaGFydFByb3BzKSB7XG4gIGNvbnN0IGNvbG9yTWFwID0ge1xuICAgIHByaW1hcnk6IHsgc3Ryb2tlOiBcIiMwMGQ0ZmZcIiwgZmlsbDogXCJyZ2JhKDAsIDIxMiwgMjU1LCAwLjIpXCIgfSxcbiAgICBzZWNvbmRhcnk6IHsgc3Ryb2tlOiBcIiNmZjAwNmJcIiwgZmlsbDogXCJyZ2JhKDI1NSwgMCwgMTA3LCAwLjIpXCIgfSxcbiAgICBhY2NlbnQ6IHsgc3Ryb2tlOiBcIiNmZmQ3MDBcIiwgZmlsbDogXCJyZ2JhKDI1NSwgMjE1LCAwLCAwLjIpXCIgfVxuICB9O1xuXG4gIGNvbnN0IEN1c3RvbVRvb2x0aXAgPSAoeyBhY3RpdmUsIHBheWxvYWQsIGxhYmVsIH06IGFueSkgPT4ge1xuICAgIGlmIChhY3RpdmUgJiYgcGF5bG9hZCAmJiBwYXlsb2FkLmxlbmd0aCkge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJnbGFzcy1kYXJrIHAtMyByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItYm9yZGVyLzUwXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPntsYWJlbH08L3A+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbVwiIHN0eWxlPXt7IGNvbG9yOiBjb2xvck1hcFtjb2xvcl0uc3Ryb2tlIH19PlxuICAgICAgICAgICAgVmFsdWU6IHtwYXlsb2FkWzBdLnZhbHVlLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICk7XG4gICAgfVxuICAgIHJldHVybiBudWxsO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPG1vdGlvbi5kaXZcbiAgICAgIGluaXRpYWw9e2FuaW1hdGVkID8geyBvcGFjaXR5OiAwLCB5OiAyMCB9IDogeyBvcGFjaXR5OiAxIH19XG4gICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSB9fVxuICAgICAgY2xhc3NOYW1lPXtjbihcInctZnVsbFwiLCBjbGFzc05hbWUpfVxuICAgICAgc3R5bGU9e3sgaGVpZ2h0IH19XG4gICAgPlxuICAgICAgPFJlc3BvbnNpdmVDb250YWluZXIgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PVwiMTAwJVwiPlxuICAgICAgICA8UmVjaGFydHNBcmVhQ2hhcnQgZGF0YT17ZGF0YX0gbWFyZ2luPXt7IHRvcDogNSwgcmlnaHQ6IDMwLCBsZWZ0OiAyMCwgYm90dG9tOiA1IH19PlxuICAgICAgICAgIDxkZWZzPlxuICAgICAgICAgICAgPGxpbmVhckdyYWRpZW50IGlkPXtgZ3JhZGllbnQtJHtjb2xvcn1gfSB4MT1cIjBcIiB5MT1cIjBcIiB4Mj1cIjBcIiB5Mj1cIjFcIj5cbiAgICAgICAgICAgICAgPHN0b3Agb2Zmc2V0PVwiNSVcIiBzdG9wQ29sb3I9e2NvbG9yTWFwW2NvbG9yXS5zdHJva2V9IHN0b3BPcGFjaXR5PXswLjN9Lz5cbiAgICAgICAgICAgICAgPHN0b3Agb2Zmc2V0PVwiOTUlXCIgc3RvcENvbG9yPXtjb2xvck1hcFtjb2xvcl0uc3Ryb2tlfSBzdG9wT3BhY2l0eT17MH0vPlxuICAgICAgICAgICAgPC9saW5lYXJHcmFkaWVudD5cbiAgICAgICAgICA8L2RlZnM+XG4gICAgICAgICAge3Nob3dHcmlkICYmIChcbiAgICAgICAgICAgIDxDYXJ0ZXNpYW5HcmlkIFxuICAgICAgICAgICAgICBzdHJva2VEYXNoYXJyYXk9XCIzIDNcIiBcbiAgICAgICAgICAgICAgc3Ryb2tlPVwicmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpXCIgXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICl9XG4gICAgICAgICAgPFhBeGlzIFxuICAgICAgICAgICAgZGF0YUtleT1cIm5hbWVcIiBcbiAgICAgICAgICAgIGF4aXNMaW5lPXtmYWxzZX1cbiAgICAgICAgICAgIHRpY2tMaW5lPXtmYWxzZX1cbiAgICAgICAgICAgIHRpY2s9e3sgZmlsbDogXCIjOWNhM2FmXCIsIGZvbnRTaXplOiAxMiB9fVxuICAgICAgICAgIC8+XG4gICAgICAgICAgPFlBeGlzIFxuICAgICAgICAgICAgYXhpc0xpbmU9e2ZhbHNlfVxuICAgICAgICAgICAgdGlja0xpbmU9e2ZhbHNlfVxuICAgICAgICAgICAgdGljaz17eyBmaWxsOiBcIiM5Y2EzYWZcIiwgZm9udFNpemU6IDEyIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgICA8VG9vbHRpcCBjb250ZW50PXs8Q3VzdG9tVG9vbHRpcCAvPn0gLz5cbiAgICAgICAgICA8QXJlYVxuICAgICAgICAgICAgdHlwZT1cIm1vbm90b25lXCJcbiAgICAgICAgICAgIGRhdGFLZXk9XCJ2YWx1ZVwiXG4gICAgICAgICAgICBzdHJva2U9e2NvbG9yTWFwW2NvbG9yXS5zdHJva2V9XG4gICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgIGZpbGw9e2B1cmwoI2dyYWRpZW50LSR7Y29sb3J9KWB9XG4gICAgICAgICAgICBhbmltYXRpb25EdXJhdGlvbj17YW5pbWF0ZWQgPyAxMDAwIDogMH1cbiAgICAgICAgICAvPlxuICAgICAgICA8L1JlY2hhcnRzQXJlYUNoYXJ0PlxuICAgICAgPC9SZXNwb25zaXZlQ29udGFpbmVyPlxuICAgIDwvbW90aW9uLmRpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJtb3Rpb24iLCJBcmVhQ2hhcnQiLCJSZWNoYXJ0c0FyZWFDaGFydCIsIkFyZWEiLCJYQXhpcyIsIllBeGlzIiwiQ2FydGVzaWFuR3JpZCIsIlRvb2x0aXAiLCJSZXNwb25zaXZlQ29udGFpbmVyIiwiY24iLCJkYXRhIiwiY2xhc3NOYW1lIiwiY29sb3IiLCJoZWlnaHQiLCJzaG93R3JpZCIsImFuaW1hdGVkIiwiY29sb3JNYXAiLCJwcmltYXJ5Iiwic3Ryb2tlIiwiZmlsbCIsInNlY29uZGFyeSIsImFjY2VudCIsIkN1c3RvbVRvb2x0aXAiLCJhY3RpdmUiLCJwYXlsb2FkIiwibGFiZWwiLCJsZW5ndGgiLCJkaXYiLCJwIiwic3R5bGUiLCJ2YWx1ZSIsInRvTG9jYWxlU3RyaW5nIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsIndpZHRoIiwibWFyZ2luIiwidG9wIiwicmlnaHQiLCJsZWZ0IiwiYm90dG9tIiwiZGVmcyIsImxpbmVhckdyYWRpZW50IiwiaWQiLCJ4MSIsInkxIiwieDIiLCJ5MiIsInN0b3AiLCJvZmZzZXQiLCJzdG9wQ29sb3IiLCJzdG9wT3BhY2l0eSIsInN0cm9rZURhc2hhcnJheSIsImRhdGFLZXkiLCJheGlzTGluZSIsInRpY2tMaW5lIiwidGljayIsImZvbnRTaXplIiwiY29udGVudCIsInR5cGUiLCJzdHJva2VXaWR0aCIsImFuaW1hdGlvbkR1cmF0aW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/charts/AreaChart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/charts/LineChart.tsx":
/*!*********************************************!*\
  !*** ./src/components/charts/LineChart.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LineChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LineChart({ data, className, color = \"primary\", height = 300, showGrid = true, animated = true }) {\n    const colorMap = {\n        primary: \"#00d4ff\",\n        secondary: \"#ff006b\",\n        accent: \"#ffd700\"\n    };\n    const CustomTooltip = ({ active, payload, label })=>{\n        if (active && payload && payload.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-dark p-3 rounded-lg border border-border/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        style: {\n                            color: colorMap[color]\n                        },\n                        children: [\n                            \"Value: \",\n                            payload[0].value.toLocaleString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: animated ? {\n            opacity: 0,\n            y: 20\n        } : {\n            opacity: 1\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-full\", className),\n        style: {\n            height\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.LineChart, {\n                data: data,\n                margin: {\n                    top: 5,\n                    right: 30,\n                    left: 20,\n                    bottom: 5\n                },\n                children: [\n                    showGrid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.CartesianGrid, {\n                        strokeDasharray: \"3 3\",\n                        stroke: \"rgba(255, 255, 255, 0.1)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.XAxis, {\n                        dataKey: \"name\",\n                        axisLine: false,\n                        tickLine: false,\n                        tick: {\n                            fill: \"#9ca3af\",\n                            fontSize: 12\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.YAxis, {\n                        axisLine: false,\n                        tickLine: false,\n                        tick: {\n                            fill: \"#9ca3af\",\n                            fontSize: 12\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Tooltip, {\n                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 29\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.Line, {\n                        type: \"monotone\",\n                        dataKey: \"value\",\n                        stroke: colorMap[color],\n                        strokeWidth: 3,\n                        dot: {\n                            fill: colorMap[color],\n                            strokeWidth: 2,\n                            r: 4\n                        },\n                        activeDot: {\n                            r: 6,\n                            stroke: colorMap[color],\n                            strokeWidth: 2\n                        },\n                        animationDuration: animated ? 1000 : 0\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\charts\\\\LineChart.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/charts/LineChart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DashboardLayout({ children, className }) {\n    const [sidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background trajectory-grid\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"transition-all duration-300\", sidebarCollapsed ? \"ml-16\" : \"ml-64\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.4,\n                                delay: 0.1\n                            },\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"space-y-6\", className),\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.header, {\n        initial: {\n            y: -20,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"glass border-b border-border/50 backdrop-blur-xl\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between px-6 py-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 max-w-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search dashboard...\",\n                                className: \"w-full pl-10 pr-4 py-2 bg-white/5 border border-border/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500/50 transition-all\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"relative p-2 rounded-lg hover:bg-white/10 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-secondary-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-1.5 h-1.5 bg-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            className: \"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-white/5 cursor-pointer transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"John Doe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Entrepreneur\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Goals\",\n        href: \"/goals\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Wealth\",\n        href: \"/wealth\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Habits\",\n        href: \"/habits\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nfunction Sidebar({ className }) {\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            x: -100,\n            opacity: 0\n        },\n        animate: {\n            x: 0,\n            opacity: 1\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed left-0 top-0 z-40 h-screen transition-all duration-300\", isCollapsed ? \"w-16\" : \"w-64\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass-dark h-full flex flex-col border-r border-border/50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-border/50\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-display font-bold text-xl bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent\",\n                                    children: \"02Bn\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsCollapsed(!isCollapsed),\n                            className: \"p-2 rounded-lg hover:bg-white/10 transition-colors\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 p-4 space-y-2\",\n                    children: navigation.map((item)=>{\n                        const Icon = item.icon;\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: item.href,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 cursor-pointer\", isActive ? \"bg-primary-500/20 text-primary-400 border border-primary-500/30\" : \"text-muted-foreground hover:text-foreground hover:bg-white/5\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 19\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.span, {\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        className: \"font-medium\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 17\n                            }, this)\n                        }, item.name, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-border/50\",\n                    children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Mission Control\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-500\",\n                                children: \"v1.0.0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Card)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Card({ children, className, hover = true, glow = \"none\" }) {\n    const glowClasses = {\n        blue: \"hover:neon-blue\",\n        magenta: \"hover:neon-magenta\",\n        gold: \"hover:neon-gold\",\n        none: \"\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        whileHover: hover ? {\n            y: -2\n        } : undefined,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"glass rounded-xl p-6 transition-all duration-300\", hover && \"hover-lift cursor-pointer\", glowClasses[glow], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/FloatingActionButton.tsx":
/*!****************************************************!*\
  !*** ./src/components/ui/FloatingActionButton.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FloatingActionButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction FloatingActionButton({ icon: Icon, onClick, className, color = \"primary\", size = \"md\", tooltip }) {\n    const colorClasses = {\n        primary: \"bg-primary-500 hover:bg-primary-600 shadow-primary-500/25\",\n        secondary: \"bg-secondary-500 hover:bg-secondary-600 shadow-secondary-500/25\",\n        accent: \"bg-accent-500 hover:bg-accent-600 shadow-accent-500/25\"\n    };\n    const sizeClasses = {\n        sm: \"w-12 h-12\",\n        md: \"w-14 h-14\",\n        lg: \"w-16 h-16\"\n    };\n    const iconSizes = {\n        sm: \"w-5 h-5\",\n        md: \"w-6 h-6\",\n        lg: \"w-7 h-7\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n        whileHover: {\n            scale: 1.1,\n            y: -2\n        },\n        whileTap: {\n            scale: 0.95\n        },\n        initial: {\n            scale: 0,\n            opacity: 0\n        },\n        animate: {\n            scale: 1,\n            opacity: 1\n        },\n        transition: {\n            type: \"spring\",\n            stiffness: 260,\n            damping: 20,\n            delay: 0.5\n        },\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"fixed bottom-6 right-6 rounded-full text-white shadow-lg transition-all duration-200 flex items-center justify-center z-50\", colorClasses[color], sizeClasses[size], className),\n        title: tooltip,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                className: iconSizes[size]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\FloatingActionButton.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute inset-0 rounded-full\",\n                initial: {\n                    scale: 0,\n                    opacity: 0.5\n                },\n                animate: {\n                    scale: 1.5,\n                    opacity: 0\n                },\n                transition: {\n                    duration: 1,\n                    repeat: Infinity\n                },\n                style: {\n                    background: `radial-gradient(circle, ${color === \"primary\" ? \"#00d4ff\" : color === \"secondary\" ? \"#ff006b\" : \"#ffd700\"}40 0%, transparent 70%)`\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\FloatingActionButton.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\FloatingActionButton.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/FloatingActionButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/HeatMap.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/HeatMap.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeatMap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction HeatMap({ data, className, color = \"primary\", weeks = 52, cellSize = 12, gap = 2 }) {\n    const colorMaps = {\n        primary: [\n            \"bg-white/5\",\n            \"bg-primary-500/20\",\n            \"bg-primary-500/40\",\n            \"bg-primary-500/60\",\n            \"bg-primary-500/80\"\n        ],\n        secondary: [\n            \"bg-white/5\",\n            \"bg-secondary-500/20\",\n            \"bg-secondary-500/40\",\n            \"bg-secondary-500/60\",\n            \"bg-secondary-500/80\"\n        ],\n        accent: [\n            \"bg-white/5\",\n            \"bg-accent-500/20\",\n            \"bg-accent-500/40\",\n            \"bg-accent-500/60\",\n            \"bg-accent-500/80\"\n        ]\n    };\n    // Generate grid for the past year (52 weeks x 7 days)\n    const generateGrid = ()=>{\n        const grid = [];\n        const today = new Date();\n        const startDate = new Date(today);\n        startDate.setDate(today.getDate() - weeks * 7);\n        for(let week = 0; week < weeks; week++){\n            const weekData = [];\n            for(let day = 0; day < 7; day++){\n                const currentDate = new Date(startDate);\n                currentDate.setDate(startDate.getDate() + week * 7 + day);\n                const dateString = currentDate.toISOString().split('T')[0];\n                const dayData = data.find((d)=>d.date === dateString);\n                weekData.push({\n                    date: dateString,\n                    level: dayData?.level || 0,\n                    value: dayData?.value || 0\n                });\n            }\n            grid.push(weekData);\n        }\n        return grid;\n    };\n    const grid = generateGrid();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"overflow-x-auto\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-1\",\n                style: {\n                    gap: `${gap}px`\n                },\n                children: grid.map((week, weekIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-1\",\n                        style: {\n                            gap: `${gap}px`\n                        },\n                        children: week.map((day, dayIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    scale: 0,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    duration: 0.2,\n                                    delay: (weekIndex * 7 + dayIndex) * 0.001\n                                },\n                                whileHover: {\n                                    scale: 1.2\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"rounded-sm cursor-pointer transition-all duration-200\", colorMaps[color][day.level]),\n                                style: {\n                                    width: `${cellSize}px`,\n                                    height: `${cellSize}px`\n                                },\n                                title: `${day.date}: ${day.value} activities`\n                            }, `${weekIndex}-${dayIndex}`, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\HeatMap.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, this))\n                    }, weekIndex, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\HeatMap.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\HeatMap.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-4 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Less\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\HeatMap.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: colorMaps[color].map((colorClass, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"rounded-sm\", colorClass),\n                                style: {\n                                    width: `${cellSize}px`,\n                                    height: `${cellSize}px`\n                                }\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\HeatMap.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\HeatMap.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"More\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\HeatMap.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\HeatMap.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\HeatMap.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/HeatMap.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/KPICard.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/KPICard.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KPICard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction KPICard({ title, value, change, changeLabel = \"vs last month\", icon: Icon, className, color = \"primary\", trend = \"neutral\" }) {\n    const colorClasses = {\n        primary: \"text-primary-500 bg-primary-500/20\",\n        secondary: \"text-secondary-500 bg-secondary-500/20\",\n        accent: \"text-accent-500 bg-accent-500/20\"\n    };\n    const trendClasses = {\n        up: \"text-green-500\",\n        down: \"text-red-500\",\n        neutral: \"text-muted-foreground\"\n    };\n    const trendSymbol = {\n        up: \"+\",\n        down: \"-\",\n        neutral: \"\"\n    };\n    const glowMapping = {\n        primary: \"blue\",\n        secondary: \"magenta\",\n        accent: \"gold\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Card__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative overflow-hidden\", className),\n        glow: glowMapping[color],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-muted-foreground mb-1\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                initial: {\n                                    scale: 0.8,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: 0.1\n                                },\n                                className: \"text-3xl font-bold font-display\",\n                                children: value\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            change !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: 0.2\n                                },\n                                className: \"flex items-center mt-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"font-medium\", trendClasses[trend]),\n                                        children: [\n                                            trendSymbol[trend],\n                                            Math.abs(change),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground ml-1\",\n                                        children: changeLabel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            scale: 0,\n                            rotate: -180\n                        },\n                        animate: {\n                            scale: 1,\n                            rotate: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.1\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"p-3 rounded-lg\", colorClasses[color]),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -right-4 -bottom-4 w-24 h-24 opacity-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"w-full h-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\KPICard.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/KPICard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ProgressBar.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/ProgressBar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProgressBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ProgressBar({ value, max = 100, className, color = \"primary\", size = \"md\", showValue = true, animated = true }) {\n    const percentage = Math.min(value / max * 100, 100);\n    const colorClasses = {\n        primary: \"from-primary-500 to-primary-400\",\n        secondary: \"from-secondary-500 to-secondary-400\",\n        accent: \"from-accent-500 to-accent-400\"\n    };\n    const sizeClasses = {\n        sm: \"h-2\",\n        md: \"h-3\",\n        lg: \"h-4\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-full\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative bg-white/10 rounded-full overflow-hidden\", sizeClasses[size]),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: animated ? {\n                        width: 0\n                    } : {\n                        width: `${percentage}%`\n                    },\n                    animate: {\n                        width: `${percentage}%`\n                    },\n                    transition: {\n                        duration: animated ? 1 : 0,\n                        ease: \"easeOut\"\n                    },\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"h-full bg-gradient-to-r rounded-full relative\", colorClasses[color]),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 shimmer opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            showValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                initial: animated ? {\n                    opacity: 0\n                } : {\n                    opacity: 1\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: animated ? 0.5 : 0\n                },\n                className: \"flex justify-between items-center mt-2 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Progress\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium\",\n                        children: [\n                            percentage.toFixed(1),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ProgressBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/StatCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/StatCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction StatCard({ title, value, subtitle, icon: Icon, className, color = \"primary\", size = \"md\" }) {\n    const colorClasses = {\n        primary: \"text-primary-500 bg-primary-500/20\",\n        secondary: \"text-secondary-500 bg-secondary-500/20\",\n        accent: \"text-accent-500 bg-accent-500/20\"\n    };\n    const sizeClasses = {\n        sm: {\n            icon: \"w-4 h-4\",\n            iconContainer: \"p-2\",\n            title: \"text-xs\",\n            value: \"text-lg\",\n            subtitle: \"text-xs\"\n        },\n        md: {\n            icon: \"w-6 h-6\",\n            iconContainer: \"p-3\",\n            title: \"text-sm\",\n            value: \"text-2xl\",\n            subtitle: \"text-sm\"\n        },\n        lg: {\n            icon: \"w-8 h-8\",\n            iconContainer: \"p-4\",\n            title: \"text-base\",\n            value: \"text-3xl\",\n            subtitle: \"text-base\"\n        }\n    };\n    const glowMapping = {\n        primary: \"blue\",\n        secondary: \"magenta\",\n        accent: \"gold\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Card__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-center\", className),\n        glow: glowMapping[color],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    scale: 0,\n                    rotate: -180\n                },\n                animate: {\n                    scale: 1,\n                    rotate: 0\n                },\n                transition: {\n                    duration: 0.5,\n                    delay: 0.1\n                },\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"mx-auto rounded-lg mb-3\", colorClasses[color], sizeClasses[size].iconContainer),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(sizeClasses[size].icon)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\StatCard.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\StatCard.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                initial: {\n                    opacity: 0,\n                    y: 10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.2\n                },\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"font-medium text-muted-foreground mb-1\", sizeClasses[size].title),\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\StatCard.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                initial: {\n                    scale: 0.8,\n                    opacity: 0\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.3\n                },\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"font-bold font-display\", sizeClasses[size].value),\n                children: value\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\StatCard.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                initial: {\n                    opacity: 0,\n                    y: 10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.4\n                },\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-muted-foreground mt-1\", sizeClasses[size].subtitle),\n                children: subtitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\StatCard.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\StatCard.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/StatCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateProgress: () => (/* binding */ calculateProgress),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getProgressColor: () => (/* binding */ getProgressColor)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount) {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD',\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(amount);\n}\nfunction formatPercentage(value) {\n    return `${(value * 100).toFixed(1)}%`;\n}\nfunction formatNumber(num) {\n    if (num >= 1e9) {\n        return `${(num / 1e9).toFixed(1)}B`;\n    }\n    if (num >= 1e6) {\n        return `${(num / 1e6).toFixed(1)}M`;\n    }\n    if (num >= 1e3) {\n        return `${(num / 1e3).toFixed(1)}K`;\n    }\n    return num.toString();\n}\nfunction calculateProgress(current, target) {\n    if (target === 0) return 0;\n    return Math.min(current / target * 100, 100);\n}\nfunction getProgressColor(progress) {\n    if (progress >= 90) return \"text-accent-500\";\n    if (progress >= 70) return \"text-primary-500\";\n    if (progress >= 50) return \"text-secondary-500\";\n    return \"text-muted-foreground\";\n}\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/clsx","vendor-chunks/recharts","vendor-chunks/@reduxjs","vendor-chunks/d3-shape","vendor-chunks/decimal.js-light","vendor-chunks/es-toolkit","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/d3-scale","vendor-chunks/reselect","vendor-chunks/d3-time-format","vendor-chunks/redux","vendor-chunks/d3-time","vendor-chunks/d3-format","vendor-chunks/d3-array","vendor-chunks/d3-color","vendor-chunks/use-sync-external-store","vendor-chunks/eventemitter3","vendor-chunks/d3-interpolate","vendor-chunks/react-is","vendor-chunks/d3-path","vendor-chunks/internmap","vendor-chunks/victory-vendor","vendor-chunks/redux-thunk"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin.NEIL%5CDesktop%5C02bn%5C02bn%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin.NEIL%5CDesktop%5C02bn%5C02bn&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();