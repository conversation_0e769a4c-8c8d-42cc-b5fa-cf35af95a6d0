"use client";

import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface FloatingActionButtonProps {
  icon: LucideIcon;
  onClick?: () => void;
  className?: string;
  color?: "primary" | "secondary" | "accent";
  size?: "sm" | "md" | "lg";
  tooltip?: string;
}

export default function FloatingActionButton({
  icon: Icon,
  onClick,
  className,
  color = "primary",
  size = "md",
  tooltip
}: FloatingActionButtonProps) {
  const colorClasses = {
    primary: "bg-primary-500 hover:bg-primary-600 shadow-primary-500/25",
    secondary: "bg-secondary-500 hover:bg-secondary-600 shadow-secondary-500/25",
    accent: "bg-accent-500 hover:bg-accent-600 shadow-accent-500/25"
  };

  const sizeClasses = {
    sm: "w-12 h-12",
    md: "w-14 h-14",
    lg: "w-16 h-16"
  };

  const iconSizes = {
    sm: "w-5 h-5",
    md: "w-6 h-6", 
    lg: "w-7 h-7"
  };

  return (
    <motion.button
      whileHover={{ scale: 1.1, y: -2 }}
      whileTap={{ scale: 0.95 }}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ 
        type: "spring", 
        stiffness: 260, 
        damping: 20,
        delay: 0.5 
      }}
      onClick={onClick}
      className={cn(
        "fixed bottom-6 right-6 rounded-full text-white shadow-lg transition-all duration-200 flex items-center justify-center z-50",
        colorClasses[color],
        sizeClasses[size],
        className
      )}
      title={tooltip}
    >
      <Icon className={iconSizes[size]} />
      
      {/* Ripple effect */}
      <motion.div
        className="absolute inset-0 rounded-full"
        initial={{ scale: 0, opacity: 0.5 }}
        animate={{ scale: 1.5, opacity: 0 }}
        transition={{ duration: 1, repeat: Infinity }}
        style={{
          background: `radial-gradient(circle, ${
            color === "primary" ? "#00d4ff" :
            color === "secondary" ? "#ff006b" : "#ffd700"
          }40 0%, transparent 70%)`
        }}
      />
    </motion.button>
  );
}
