"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface HeatMapData {
  date: string;
  value: number;
  level: 0 | 1 | 2 | 3 | 4; // 0 = no activity, 4 = highest activity
}

interface HeatMapProps {
  data: HeatMapData[];
  className?: string;
  color?: "primary" | "secondary" | "accent";
  weeks?: number;
  cellSize?: number;
  gap?: number;
}

export default function HeatMap({
  data,
  className,
  color = "primary",
  weeks = 52,
  cellSize = 12,
  gap = 2
}: HeatMapProps) {
  const colorMaps = {
    primary: [
      "bg-white/5",
      "bg-primary-500/20",
      "bg-primary-500/40", 
      "bg-primary-500/60",
      "bg-primary-500/80"
    ],
    secondary: [
      "bg-white/5",
      "bg-secondary-500/20",
      "bg-secondary-500/40",
      "bg-secondary-500/60", 
      "bg-secondary-500/80"
    ],
    accent: [
      "bg-white/5",
      "bg-accent-500/20",
      "bg-accent-500/40",
      "bg-accent-500/60",
      "bg-accent-500/80"
    ]
  };

  // Generate grid for the past year (52 weeks x 7 days)
  const generateGrid = () => {
    const grid = [];
    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - (weeks * 7));

    for (let week = 0; week < weeks; week++) {
      const weekData = [];
      for (let day = 0; day < 7; day++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + (week * 7) + day);
        
        const dateString = currentDate.toISOString().split('T')[0];
        const dayData = data.find(d => d.date === dateString);
        
        weekData.push({
          date: dateString,
          level: dayData?.level || 0,
          value: dayData?.value || 0
        });
      }
      grid.push(weekData);
    }
    return grid;
  };

  const grid = generateGrid();

  return (
    <div className={cn("overflow-x-auto", className)}>
      <div className="flex space-x-1" style={{ gap: `${gap}px` }}>
        {grid.map((week, weekIndex) => (
          <div key={weekIndex} className="flex flex-col space-y-1" style={{ gap: `${gap}px` }}>
            {week.map((day, dayIndex) => (
              <motion.div
                key={`${weekIndex}-${dayIndex}`}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ 
                  duration: 0.2, 
                  delay: (weekIndex * 7 + dayIndex) * 0.001 
                }}
                whileHover={{ scale: 1.2 }}
                className={cn(
                  "rounded-sm cursor-pointer transition-all duration-200",
                  colorMaps[color][day.level]
                )}
                style={{ 
                  width: `${cellSize}px`, 
                  height: `${cellSize}px` 
                }}
                title={`${day.date}: ${day.value} activities`}
              />
            ))}
          </div>
        ))}
      </div>
      
      {/* Legend */}
      <div className="flex items-center justify-between mt-4 text-xs text-muted-foreground">
        <span>Less</span>
        <div className="flex space-x-1">
          {colorMaps[color].map((colorClass, index) => (
            <div
              key={index}
              className={cn("rounded-sm", colorClass)}
              style={{ width: `${cellSize}px`, height: `${cellSize}px` }}
            />
          ))}
        </div>
        <span>More</span>
      </div>
    </div>
  );
}
