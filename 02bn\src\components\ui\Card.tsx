"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  glow?: "blue" | "magenta" | "gold" | "none";
}

export default function Card({ 
  children, 
  className, 
  hover = true,
  glow = "none" 
}: CardProps) {
  const glowClasses = {
    blue: "hover:neon-blue",
    magenta: "hover:neon-magenta", 
    gold: "hover:neon-gold",
    none: ""
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={hover ? { y: -2 } : undefined}
      className={cn(
        "glass rounded-xl p-6 transition-all duration-300",
        hover && "hover-lift cursor-pointer",
        glowClasses[glow],
        className
      )}
    >
      {children}
    </motion.div>
  );
}
