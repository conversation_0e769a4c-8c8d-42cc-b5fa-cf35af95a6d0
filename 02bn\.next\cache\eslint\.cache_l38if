[{"C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\layout\\DashboardLayout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\layout\\Header.tsx": "4", "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\layout\\Sidebar.tsx": "5", "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\ui\\Card.tsx": "6", "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\ui\\KPICard.tsx": "7", "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\ui\\ProgressBar.tsx": "8", "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\lib\\utils.ts": "9"}, {"size": 1106, "mtime": 1751929421418, "results": "10", "hashOfConfig": "11"}, {"size": 6842, "mtime": 1751929551292, "results": "12", "hashOfConfig": "11"}, {"size": 1033, "mtime": 1751929471738, "results": "13", "hashOfConfig": "11"}, {"size": 2536, "mtime": 1751929464097, "results": "14", "hashOfConfig": "11"}, {"size": 3912, "mtime": 1751929449977, "results": "15", "hashOfConfig": "11"}, {"size": 894, "mtime": 1751929480075, "results": "16", "hashOfConfig": "11"}, {"size": 2820, "mtime": 1751929818958, "results": "17", "hashOfConfig": "11"}, {"size": 1913, "mtime": 1751929491421, "results": "18", "hashOfConfig": "11"}, {"size": 1554, "mtime": 1751929405216, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gkch44", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\app\\page.tsx", ["47"], [], "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\layout\\DashboardLayout.tsx", ["48"], [], "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\ui\\KPICard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\components\\ui\\ProgressBar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\02bn\\02bn\\src\\lib\\utils.ts", ["49", "50"], [], {"ruleId": "51", "severity": 2, "message": "52", "line": 17, "column": 26, "nodeType": null, "messageId": "53", "endLine": 17, "endColumn": 38}, {"ruleId": "51", "severity": 2, "message": "54", "line": 15, "column": 28, "nodeType": null, "messageId": "53", "endLine": 15, "endColumn": 47}, {"ruleId": "55", "severity": 2, "message": "56", "line": 50, "column": 46, "nodeType": "57", "messageId": "58", "endLine": 50, "endColumn": 49, "suggestions": "59"}, {"ruleId": "55", "severity": 2, "message": "56", "line": 50, "column": 56, "nodeType": "57", "messageId": "58", "endLine": 50, "endColumn": 59, "suggestions": "60"}, "@typescript-eslint/no-unused-vars", "'formatNumber' is defined but never used.", "unusedVar", "'setSidebarCollapsed' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["61", "62"], ["63", "64"], {"messageId": "65", "fix": "66", "desc": "67"}, {"messageId": "68", "fix": "69", "desc": "70"}, {"messageId": "65", "fix": "71", "desc": "67"}, {"messageId": "68", "fix": "72", "desc": "70"}, "suggestUnknown", {"range": "73", "text": "74"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "75", "text": "76"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "77", "text": "74"}, {"range": "78", "text": "76"}, [1317, 1320], "unknown", [1317, 1320], "never", [1327, 1330], [1327, 1330]]