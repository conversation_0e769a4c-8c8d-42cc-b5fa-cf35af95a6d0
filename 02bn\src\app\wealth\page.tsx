"use client";

import DashboardLayout from "@/components/layout/DashboardLayout";
import Card from "@/components/ui/Card";
import KPICard from "@/components/ui/KPICard";
import AreaChart from "@/components/charts/AreaChart";
import LineChart from "@/components/charts/LineChart";
import { 
  DollarSign, 
  TrendingUp, 
  Pie<PERSON>hart,
  Wallet,
  CreditCard,
  Building,
  Coins
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";

// Mock wealth data
const wealthData = {
  netWorth: 125000,
  totalAssets: 180000,
  totalLiabilities: 55000,
  monthlyIncome: 8500,
  monthlyExpenses: 4200,
  savingsRate: 0.51
};

const netWorthHistory = [
  { name: "Jan", value: 95000 },
  { name: "Feb", value: 102000 },
  { name: "Mar", value: 108000 },
  { name: "Apr", value: 115000 },
  { name: "May", value: 120000 },
  { name: "<PERSON>", value: 125000 },
  { name: "<PERSON>", value: 128000 },
  { name: "Aug", value: 132000 },
  { name: "Sep", value: 135000 },
  { name: "Oct", value: 140000 },
  { name: "Nov", value: 142000 },
  { name: "Dec", value: 145000 },
];

const incomeExpenseData = [
  { name: "Jan", income: 8000, expenses: 4100 },
  { name: "Feb", income: 8200, expenses: 4000 },
  { name: "Mar", income: 8100, expenses: 4200 },
  { name: "Apr", income: 8300, expenses: 4150 },
  { name: "May", income: 8200, expenses: 4100 },
  { name: "Jun", income: 8500, expenses: 4200 },
];

const assetBreakdown = [
  { category: "Investments", amount: 85000, percentage: 47 },
  { category: "Cash & Savings", amount: 35000, percentage: 19 },
  { category: "Real Estate", amount: 45000, percentage: 25 },
  { category: "Other Assets", amount: 15000, percentage: 8 }
];

export default function WealthPage() {
  return (
    <DashboardLayout>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold font-display bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 bg-clip-text text-transparent mb-2">
          Wealth Dashboard
        </h1>
        <p className="text-muted-foreground text-lg">
          Track your financial progress and build lasting wealth.
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <KPICard
          title="Net Worth"
          value={formatCurrency(wealthData.netWorth)}
          change={12.5}
          icon={DollarSign}
          color="primary"
          trend="up"
        />
        <KPICard
          title="Total Assets"
          value={formatCurrency(wealthData.totalAssets)}
          change={8.2}
          icon={TrendingUp}
          color="secondary"
          trend="up"
        />
        <KPICard
          title="Monthly Income"
          value={formatCurrency(wealthData.monthlyIncome)}
          change={5.1}
          icon={Wallet}
          color="accent"
          trend="up"
        />
        <KPICard
          title="Savings Rate"
          value={`${(wealthData.savingsRate * 100).toFixed(0)}%`}
          change={2.3}
          icon={PieChart}
          color="primary"
          trend="up"
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card glow="blue">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2 text-primary-500" />
            Net Worth Growth
          </h3>
          <AreaChart data={netWorthHistory} color="primary" height={300} />
        </Card>

        <Card glow="magenta">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <DollarSign className="w-5 h-5 mr-2 text-secondary-500" />
            Income vs Expenses
          </h3>
          <div className="space-y-4">
            <LineChart 
              data={incomeExpenseData.map(d => ({ name: d.name, value: d.income }))} 
              color="secondary" 
              height={140} 
            />
            <LineChart 
              data={incomeExpenseData.map(d => ({ name: d.name, value: d.expenses }))} 
              color="accent" 
              height={140} 
            />
          </div>
        </Card>
      </div>

      {/* Asset Breakdown & Financial Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card glow="gold">
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <PieChart className="w-5 h-5 mr-2 text-accent-500" />
              Asset Allocation
            </h3>
            <div className="space-y-4">
              {assetBreakdown.map((asset, index) => (
                <div key={asset.category} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{asset.category}</span>
                    <div className="text-right">
                      <div className="font-semibold">{formatCurrency(asset.amount)}</div>
                      <div className="text-sm text-muted-foreground">{asset.percentage}%</div>
                    </div>
                  </div>
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-1000 ${
                        index === 0 ? 'bg-primary-500' :
                        index === 1 ? 'bg-secondary-500' :
                        index === 2 ? 'bg-accent-500' : 'bg-muted-foreground'
                      }`}
                      style={{ width: `${asset.percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>

        <div className="space-y-6">
          <Card glow="blue">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Building className="w-5 h-5 mr-2 text-primary-500" />
              Liabilities
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Mortgage</span>
                <span className="font-medium">{formatCurrency(45000)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Student Loans</span>
                <span className="font-medium">{formatCurrency(8000)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Credit Cards</span>
                <span className="font-medium">{formatCurrency(2000)}</span>
              </div>
              <div className="border-t border-border/50 pt-2 mt-2">
                <div className="flex justify-between items-center font-semibold">
                  <span>Total Liabilities</span>
                  <span className="text-red-400">{formatCurrency(wealthData.totalLiabilities)}</span>
                </div>
              </div>
            </div>
          </Card>

          <Card glow="magenta">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Coins className="w-5 h-5 mr-2 text-secondary-500" />
              Monthly Cash Flow
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Income</span>
                <span className="font-medium text-green-400">{formatCurrency(wealthData.monthlyIncome)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Expenses</span>
                <span className="font-medium text-red-400">-{formatCurrency(wealthData.monthlyExpenses)}</span>
              </div>
              <div className="border-t border-border/50 pt-2 mt-2">
                <div className="flex justify-between items-center font-semibold">
                  <span>Net Cash Flow</span>
                  <span className="text-primary-400">
                    {formatCurrency(wealthData.monthlyIncome - wealthData.monthlyExpenses)}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
