"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface ProgressBarProps {
  value: number;
  max?: number;
  className?: string;
  color?: "primary" | "secondary" | "accent";
  size?: "sm" | "md" | "lg";
  showValue?: boolean;
  animated?: boolean;
}

export default function ProgressBar({
  value,
  max = 100,
  className,
  color = "primary",
  size = "md",
  showValue = true,
  animated = true
}: ProgressBarProps) {
  const percentage = Math.min((value / max) * 100, 100);
  
  const colorClasses = {
    primary: "from-primary-500 to-primary-400",
    secondary: "from-secondary-500 to-secondary-400", 
    accent: "from-accent-500 to-accent-400"
  };

  const sizeClasses = {
    sm: "h-2",
    md: "h-3",
    lg: "h-4"
  };

  return (
    <div className={cn("w-full", className)}>
      <div className={cn(
        "relative bg-white/10 rounded-full overflow-hidden",
        sizeClasses[size]
      )}>
        <motion.div
          initial={animated ? { width: 0 } : { width: `${percentage}%` }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: animated ? 1 : 0, ease: "easeOut" }}
          className={cn(
            "h-full bg-gradient-to-r rounded-full relative",
            colorClasses[color]
          )}
        >
          {/* Shimmer effect */}
          <div className="absolute inset-0 shimmer opacity-30" />
        </motion.div>
      </div>
      
      {showValue && (
        <motion.div
          initial={animated ? { opacity: 0 } : { opacity: 1 }}
          animate={{ opacity: 1 }}
          transition={{ delay: animated ? 0.5 : 0 }}
          className="flex justify-between items-center mt-2 text-sm"
        >
          <span className="text-muted-foreground">Progress</span>
          <span className="font-medium">{percentage.toFixed(1)}%</span>
        </motion.div>
      )}
    </div>
  );
}
