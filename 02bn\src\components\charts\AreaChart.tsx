"use client";

import { motion } from "framer-motion";
import {
  AreaChart as RechartsAreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { cn } from "@/lib/utils";

interface DataPoint {
  name: string;
  value: number;
  [key: string]: string | number;
}

interface AreaChartProps {
  data: DataPoint[];
  className?: string;
  color?: "primary" | "secondary" | "accent";
  height?: number;
  showGrid?: boolean;
  animated?: boolean;
}

export default function AreaChart({
  data,
  className,
  color = "primary",
  height = 300,
  showGrid = true,
  animated = true
}: AreaChartProps) {
  const colorMap = {
    primary: { stroke: "#00d4ff", fill: "rgba(0, 212, 255, 0.2)" },
    secondary: { stroke: "#ff006b", fill: "rgba(255, 0, 107, 0.2)" },
    accent: { stroke: "#ffd700", fill: "rgba(255, 215, 0, 0.2)" }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="glass-dark p-3 rounded-lg border border-border/50">
          <p className="text-sm font-medium">{label}</p>
          <p className="text-sm" style={{ color: colorMap[color].stroke }}>
            Value: {payload[0].value.toLocaleString()}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <motion.div
      initial={animated ? { opacity: 0, y: 20 } : { opacity: 1 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={cn("w-full", className)}
      style={{ height }}
    >
      <ResponsiveContainer width="100%" height="100%">
        <RechartsAreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id={`gradient-${color}`} x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={colorMap[color].stroke} stopOpacity={0.3}/>
              <stop offset="95%" stopColor={colorMap[color].stroke} stopOpacity={0}/>
            </linearGradient>
          </defs>
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="rgba(255, 255, 255, 0.1)" 
            />
          )}
          <XAxis 
            dataKey="name" 
            axisLine={false}
            tickLine={false}
            tick={{ fill: "#9ca3af", fontSize: 12 }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fill: "#9ca3af", fontSize: 12 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type="monotone"
            dataKey="value"
            stroke={colorMap[color].stroke}
            strokeWidth={2}
            fill={`url(#gradient-${color})`}
            animationDuration={animated ? 1000 : 0}
          />
        </RechartsAreaChart>
      </ResponsiveContainer>
    </motion.div>
  );
}
