# 02Bn - Zero to Billion Dashboard

A comprehensive personal growth and wealth tracking dashboard built with Next.js, TypeScript, and modern web technologies. Track your journey from zero to billion with precision and purpose.

![02Bn Dashboard](https://via.placeholder.com/800x400/1a1a1a/00d4ff?text=02Bn+Dashboard)

## ✨ Features

### 🎯 Core Functionality
- **Personal Dashboard** - Central command center with key performance indicators
- **Goal Tracking System** - Monitor progress toward financial and personal goals
- **Wealth Management** - Track net worth, income, investments, and financial metrics
- **Habit Tracking** - Build consistency with daily habit monitoring and streak counters
- **Data Visualization** - Interactive charts, progress bars, and heatmaps

### 🎨 Design & UX
- **Dark Theme** - Sleek dark interface with neon accent colors
- **Glassmorphism Effects** - Modern translucent backgrounds and blur effects
- **Responsive Design** - Optimized for desktop, tablet, and mobile devices
- **Smooth Animations** - Framer Motion powered micro-interactions
- **Mission Control Aesthetic** - Command center inspired design language

### 🛠 Technical Features
- **Next.js 15** - Latest React framework with App Router
- **TypeScript** - Full type safety and developer experience
- **Tailwind CSS** - Utility-first styling with custom design system
- **Framer Motion** - Smooth animations and transitions
- **Recharts** - Interactive data visualization components
- **Lucide Icons** - Beautiful, consistent iconography

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd 02bn
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📱 Pages & Features

### Dashboard (`/`)
- **Trajectory Hub** - Central metrics and progress tracking
- **KPI Cards** - Net worth, income, goals, and productivity scores
- **Quick Stats** - Weekly goals, achievements, and success rates
- **Interactive Charts** - Wealth growth and income visualization
- **Activity Feed** - Recent updates and milestones
- **Today's Focus** - Daily tasks and priorities

### Goals (`/goals`)
- **Goal Management** - Create, track, and complete personal goals
- **Progress Visualization** - Visual progress bars and completion tracking
- **Category Organization** - Financial, learning, health, and personal goals
- **Deadline Tracking** - Time-based goal monitoring
- **Achievement History** - Record of completed goals and milestones

### Wealth (`/wealth`)
- **Net Worth Tracking** - Comprehensive wealth monitoring
- **Asset Allocation** - Investment portfolio breakdown
- **Income vs Expenses** - Cash flow analysis and trends
- **Liability Management** - Debt tracking and payoff planning
- **Financial Charts** - Historical performance and projections

### Habits (`/habits`)
- **Daily Habit Tracking** - Mark completion and build streaks
- **Habit Heatmaps** - Visual representation of consistency
- **Streak Counters** - Motivation through consecutive day tracking
- **Weekly Summaries** - Performance analysis and insights
- **Category Organization** - Health, learning, wellness, and personal habits

## 🎨 Design System

### Color Palette
- **Electric Blue** (`#00D4FF`) - Primary brand color
- **Vibrant Magenta** (`#FF006B`) - Secondary accent
- **Gold** (`#FFD700`) - Success and achievement color
- **Dark Background** - Deep space aesthetic

### Typography
- **Inter** - Primary font for body text and UI elements
- **Montserrat** - Display font for headings and emphasis

### Components
- **Glass Cards** - Translucent containers with blur effects
- **Neon Glows** - Subtle lighting effects on interactive elements
- **Smooth Animations** - Framer Motion powered transitions
- **Responsive Grid** - Flexible layouts for all screen sizes

## 🛠 Development

### Project Structure
```
src/
├── app/                 # Next.js App Router pages
├── components/          # Reusable UI components
│   ├── charts/         # Data visualization components
│   ├── layout/         # Layout and navigation components
│   └── ui/             # Basic UI components
├── lib/                # Utility functions and helpers
└── styles/             # Global styles and CSS
```

### Key Technologies
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **Framer Motion** - Animation library
- **Recharts** - Chart components
- **Lucide React** - Icon library

## 📈 Performance

- **Optimized Images** - Next.js Image component for performance
- **Code Splitting** - Automatic route-based code splitting
- **Tree Shaking** - Unused code elimination
- **Modern CSS** - CSS Grid, Flexbox, and custom properties
- **Responsive Design** - Mobile-first approach

## 🔮 Future Enhancements

- **Data Persistence** - Database integration for user data
- **Authentication** - User accounts and secure login
- **Real-time Updates** - Live data synchronization
- **Mobile App** - React Native companion app
- **AI Insights** - Machine learning powered recommendations
- **Social Features** - Goal sharing and community challenges

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support, email <EMAIL> or join our Discord community.

---

**Built with ❤️ for ambitious individuals on their journey to success.**
