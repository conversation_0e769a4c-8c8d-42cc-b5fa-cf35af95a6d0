"use client";

import DashboardLayout from "@/components/layout/DashboardLayout";
import Card from "@/components/ui/Card";
import StatCard from "@/components/ui/StatCard";
import HeatMap from "@/components/ui/HeatMap";
import ProgressBar from "@/components/ui/ProgressBar";
import { 
  Activity, 
  Flame, 
  Target,
  Clock,
  CheckCircle,
  Plus,
  Calendar,
  TrendingUp
} from "lucide-react";
import { motion } from "framer-motion";

// Mock habits data
const habits = [
  {
    id: 1,
    name: "Morning Workout",
    description: "30 minutes of exercise",
    streak: 12,
    completedToday: true,
    weeklyTarget: 5,
    weeklyCompleted: 4,
    category: "Health",
    color: "primary" as const
  },
  {
    id: 2,
    name: "Read for 30 minutes",
    description: "Daily reading habit",
    streak: 8,
    completedToday: false,
    weeklyTarget: 7,
    weeklyCompleted: 6,
    category: "Learning",
    color: "secondary" as const
  },
  {
    id: 3,
    name: "Meditate",
    description: "10 minutes mindfulness",
    streak: 15,
    completedToday: true,
    weeklyTarget: 7,
    weeklyCompleted: 7,
    category: "Wellness",
    color: "accent" as const
  },
  {
    id: 4,
    name: "Journal",
    description: "Reflect on the day",
    streak: 5,
    completedToday: false,
    weeklyTarget: 7,
    weeklyCompleted: 5,
    category: "Personal",
    color: "primary" as const
  }
];

// Generate mock habit tracking data for heatmap
const generateHabitData = (habitId: number) => {
  return Array.from({ length: 365 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - (365 - i));
    const completed = Math.random() > 0.3; // 70% completion rate
    return {
      date: date.toISOString().split('T')[0],
      value: completed ? 1 : 0,
      level: completed ? (Math.floor(Math.random() * 3) + 2) as 2 | 3 | 4 : 0 as 0
    };
  });
};

const stats = {
  totalHabits: habits.length,
  completedToday: habits.filter(h => h.completedToday).length,
  longestStreak: Math.max(...habits.map(h => h.streak)),
  averageCompletion: 78
};

export default function HabitsPage() {
  return (
    <DashboardLayout>
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-4xl font-bold font-display bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 bg-clip-text text-transparent mb-2">
            Habit Tracking
          </h1>
          <p className="text-muted-foreground text-lg">
            Build consistency and track your daily progress.
          </p>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="flex items-center space-x-2 px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors"
        >
          <Plus className="w-5 h-5" />
          <span>Add Habit</span>
        </motion.button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Habits"
          value={stats.totalHabits}
          icon={Activity}
          color="primary"
        />
        <StatCard
          title="Completed Today"
          value={`${stats.completedToday}/${stats.totalHabits}`}
          icon={CheckCircle}
          color="accent"
        />
        <StatCard
          title="Longest Streak"
          value={`${stats.longestStreak} days`}
          icon={Flame}
          color="secondary"
        />
        <StatCard
          title="Avg Completion"
          value={`${stats.averageCompletion}%`}
          icon={TrendingUp}
          color="primary"
        />
      </div>

      {/* Today's Habits */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold font-display mb-6">Today's Habits</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {habits.map((habit, index) => (
            <motion.div
              key={habit.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className={`${habit.completedToday ? 'border-green-500/50' : ''}`} 
                    glow={habit.color === "primary" ? "blue" : habit.color === "secondary" ? "magenta" : "gold"}>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors ${
                        habit.completedToday 
                          ? 'bg-green-500 border-green-500' 
                          : 'border-muted-foreground hover:border-primary-500'
                      }`}
                    >
                      {habit.completedToday && <CheckCircle className="w-4 h-4 text-white" />}
                    </motion.button>
                    <div>
                      <h3 className="font-semibold">{habit.name}</h3>
                      <p className="text-sm text-muted-foreground">{habit.description}</p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center space-x-1 text-sm">
                      <Flame className="w-4 h-4 text-orange-500" />
                      <span className="font-medium">{habit.streak}</span>
                    </div>
                    <span className="text-xs text-muted-foreground">day streak</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span>Weekly Progress</span>
                    <span>{habit.weeklyCompleted}/{habit.weeklyTarget}</span>
                  </div>
                  <ProgressBar
                    value={habit.weeklyCompleted}
                    max={habit.weeklyTarget}
                    color={habit.color}
                    size="sm"
                    showValue={false}
                  />
                </div>
                
                <div className="mt-3 flex items-center justify-between text-xs text-muted-foreground">
                  <span className={`px-2 py-1 rounded-full ${
                    habit.color === "primary" ? "bg-primary-500/20 text-primary-400" :
                    habit.color === "secondary" ? "bg-secondary-500/20 text-secondary-400" :
                    "bg-accent-500/20 text-accent-400"
                  }`}>
                    {habit.category}
                  </span>
                  <span>{((habit.weeklyCompleted / habit.weeklyTarget) * 100).toFixed(0)}% this week</span>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Habit Heatmaps */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold font-display">Habit History</h2>
        
        {habits.slice(0, 2).map((habit, index) => (
          <Card key={habit.id} glow={habit.color === "primary" ? "blue" : "magenta"}>
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold">{habit.name}</h3>
                <p className="text-sm text-muted-foreground">Last 12 months activity</p>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-2">
                  <Flame className="w-5 h-5 text-orange-500" />
                  <span className="text-2xl font-bold">{habit.streak}</span>
                </div>
                <span className="text-sm text-muted-foreground">current streak</span>
              </div>
            </div>
            
            <HeatMap 
              data={generateHabitData(habit.id)} 
              color={habit.color}
              weeks={52}
              cellSize={12}
            />
          </Card>
        ))}
      </div>

      {/* Weekly Summary */}
      <div className="mt-8">
        <Card glow="gold">
          <h3 className="text-xl font-bold font-display mb-6 flex items-center">
            <Calendar className="w-6 h-6 mr-2 text-accent-500" />
            This Week's Summary
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold text-primary-500">Most Consistent</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Meditate</span>
                  <span className="text-sm font-medium">7/7 days</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Read</span>
                  <span className="text-sm font-medium">6/7 days</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold text-secondary-500">Needs Attention</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Journal</span>
                  <span className="text-sm font-medium">5/7 days</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Workout</span>
                  <span className="text-sm font-medium">4/5 days</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold text-accent-500">Weekly Stats</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Total Completions</span>
                  <span className="text-sm font-medium">22/26</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Success Rate</span>
                  <span className="text-sm font-medium">85%</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </DashboardLayout>
  );
}
