"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pbi5ORUlMXFxEZXNrdG9wXFwwMmJuXFwwMmJuXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcbmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Goals\",\n        href: \"/goals\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Wealth\",\n        href: \"/wealth\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Habits\",\n        href: \"/habits\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    }\n];\nfunction Sidebar(param) {\n    let { className } = param;\n    _s();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            x: -100,\n            opacity: 0\n        },\n        animate: {\n            x: 0,\n            opacity: 1\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed left-0 top-0 z-40 h-screen transition-all duration-300\", isCollapsed ? \"w-16\" : \"w-64\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass-dark h-full flex flex-col border-r border-border/50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-border/50\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-display font-bold text-xl bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent\",\n                                    children: \"02Bn\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsCollapsed(!isCollapsed),\n                            className: \"p-2 rounded-lg hover:bg-white/10 transition-colors\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 p-4 space-y-2\",\n                    children: navigation.map((item)=>{\n                        const Icon = item.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.a, {\n                            href: item.href,\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            whileTap: {\n                                scale: 0.98\n                            },\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200\", item.current ? \"bg-primary-500/20 text-primary-400 border border-primary-500/30\" : \"text-muted-foreground hover:text-foreground hover:bg-white/5\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-5 h-5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.span, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        opacity: 1\n                                    },\n                                    className: \"font-medium\",\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-border/50\",\n                    children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Mission Control\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-500\",\n                                children: \"v1.0.0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"H8+9ci9wlHDqN9fWiVXPAjiDZkE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Sidebar.tsx\n"));

/***/ })

});