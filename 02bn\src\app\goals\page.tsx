"use client";

import DashboardLayout from "@/components/layout/DashboardLayout";
import Card from "@/components/ui/Card";
import ProgressBar from "@/components/ui/ProgressBar";
import StatCard from "@/components/ui/StatCard";
import { 
  Target, 
  Plus, 
  Calendar,
  TrendingUp,
  CheckCircle,
  Clock,
  DollarSign,
  BookO<PERSON>,
  Dumbbell
} from "lucide-react";
import { motion } from "framer-motion";

// Mock goals data
const goals = [
  {
    id: 1,
    title: "Reach $200K Net Worth",
    description: "Build wealth through investments and savings",
    category: "Financial",
    target: 200000,
    current: 125000,
    deadline: "2024-12-31",
    icon: DollarSign,
    color: "primary" as const
  },
  {
    id: 2,
    title: "Read 24 Books This Year",
    description: "Expand knowledge through consistent reading",
    category: "Learning",
    target: 24,
    current: 16,
    deadline: "2024-12-31",
    icon: BookOpen,
    color: "secondary" as const
  },
  {
    id: 3,
    title: "Exercise 5 Days Per Week",
    description: "Maintain physical health and fitness",
    category: "Health",
    target: 260, // 5 days * 52 weeks
    current: 180,
    deadline: "2024-12-31",
    icon: Du<PERSON>bell,
    color: "accent" as const
  }
];

const stats = {
  totalGoals: 12,
  completedGoals: 8,
  inProgress: 4,
  averageProgress: 67
};

export default function GoalsPage() {
  return (
    <DashboardLayout>
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-4xl font-bold font-display bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 bg-clip-text text-transparent mb-2">
            Goal Tracking
          </h1>
          <p className="text-muted-foreground text-lg">
            Monitor your progress toward achieving your biggest aspirations.
          </p>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="flex items-center space-x-2 px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors"
        >
          <Plus className="w-5 h-5" />
          <span>Add Goal</span>
        </motion.button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Goals"
          value={stats.totalGoals}
          icon={Target}
          color="primary"
        />
        <StatCard
          title="Completed"
          value={stats.completedGoals}
          icon={CheckCircle}
          color="accent"
        />
        <StatCard
          title="In Progress"
          value={stats.inProgress}
          icon={Clock}
          color="secondary"
        />
        <StatCard
          title="Avg Progress"
          value={`${stats.averageProgress}%`}
          icon={TrendingUp}
          color="primary"
        />
      </div>

      {/* Goals Grid */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold font-display">Active Goals</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {goals.map((goal, index) => {
            const progress = (goal.current / goal.target) * 100;
            const daysLeft = Math.ceil((new Date(goal.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
            
            return (
              <motion.div
                key={goal.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className="h-full" glow={goal.color === "primary" ? "blue" : goal.color === "secondary" ? "magenta" : "gold"}>
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${
                        goal.color === "primary" ? "bg-primary-500/20 text-primary-500" :
                        goal.color === "secondary" ? "bg-secondary-500/20 text-secondary-500" :
                        "bg-accent-500/20 text-accent-500"
                      }`}>
                        <goal.icon className="w-5 h-5" />
                      </div>
                      <div>
                        <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                          {goal.category}
                        </span>
                      </div>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      daysLeft > 30 ? "bg-green-500/20 text-green-400" :
                      daysLeft > 7 ? "bg-yellow-500/20 text-yellow-400" :
                      "bg-red-500/20 text-red-400"
                    }`}>
                      {daysLeft} days left
                    </span>
                  </div>
                  
                  <h3 className="text-lg font-semibold mb-2">{goal.title}</h3>
                  <p className="text-sm text-muted-foreground mb-4">{goal.description}</p>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between items-center text-sm">
                      <span>Progress</span>
                      <span className="font-medium">
                        {goal.current.toLocaleString()} / {goal.target.toLocaleString()}
                      </span>
                    </div>
                    
                    <ProgressBar
                      value={goal.current}
                      max={goal.target}
                      color={goal.color}
                      animated={true}
                    />
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>Due: {new Date(goal.deadline).toLocaleDateString()}</span>
                      </div>
                      <span>{progress.toFixed(1)}% complete</span>
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="mt-8">
        <Card glow="blue">
          <h3 className="text-xl font-bold font-display mb-6">Recent Goal Activity</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-4 p-3 rounded-lg bg-white/5">
              <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Updated "Reach $200K Net Worth"</p>
                <p className="text-xs text-muted-foreground">Added $5,000 to investment portfolio</p>
              </div>
              <span className="text-xs text-muted-foreground">2 hours ago</span>
            </div>
            
            <div className="flex items-center space-x-4 p-3 rounded-lg bg-white/5">
              <div className="w-2 h-2 bg-secondary-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Completed "Read 24 Books This Year"</p>
                <p className="text-xs text-muted-foreground">Finished reading "Atomic Habits"</p>
              </div>
              <span className="text-xs text-muted-foreground">1 day ago</span>
            </div>
            
            <div className="flex items-center space-x-4 p-3 rounded-lg bg-white/5">
              <div className="w-2 h-2 bg-accent-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Updated "Exercise 5 Days Per Week"</p>
                <p className="text-xs text-muted-foreground">Completed strength training session</p>
              </div>
              <span className="text-xs text-muted-foreground">2 days ago</span>
            </div>
          </div>
        </Card>
      </div>
    </DashboardLayout>
  );
}
