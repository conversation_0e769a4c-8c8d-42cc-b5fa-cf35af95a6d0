import type { Metada<PERSON> } from "next";
import { Inter, Montserrat } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const montserrat = Montserrat({
  subsets: ["latin"],
  variable: "--font-montserrat",
  display: "swap",
});

export const metadata: Metadata = {
  title: "02Bn - Zero to Billion",
  description: "Comprehensive personal growth and wealth tracking dashboard",
  keywords: ["wealth tracking", "personal growth", "dashboard", "goals", "productivity"],
  authors: [{ name: "02Bn Team" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#00d4ff",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${inter.variable} ${montserrat.variable} font-sans antialiased min-h-screen bg-background text-foreground`}
      >
        <div className="relative min-h-screen command-center">
          {children}
        </div>
      </body>
    </html>
  );
}
