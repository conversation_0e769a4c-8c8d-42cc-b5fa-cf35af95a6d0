"use client";

import DashboardLayout from "@/components/layout/DashboardLayout";
import KPICard from "@/components/ui/KPICard";
import Card from "@/components/ui/Card";
import ProgressBar from "@/components/ui/ProgressBar";
import {
  DollarSign,
  TrendingUp,
  Target,
  Activity,
  Calendar,
  Award,
  Zap,
  BarChart3
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";

// Mock data - in a real app, this would come from an API
const mockData = {
  netWorth: 125000,
  monthlyIncome: 8500,
  goalProgress: 68,
  habitsCompleted: 12,
  totalHabits: 18,
  weeklyGoals: 4,
  completedGoals: 3,
  productivityScore: 85
};

export default function Home() {
  return (
    <DashboardLayout>
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold font-display bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 bg-clip-text text-transparent mb-2">
          Welcome to Mission Control
        </h1>
        <p className="text-muted-foreground text-lg">
          Track your journey from zero to billion with precision and purpose.
        </p>
      </div>

      {/* KPI Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <KPICard
          title="Net Worth"
          value={formatCurrency(mockData.netWorth)}
          change={12.5}
          icon={DollarSign}
          color="primary"
          trend="up"
        />
        <KPICard
          title="Monthly Income"
          value={formatCurrency(mockData.monthlyIncome)}
          change={8.2}
          icon={TrendingUp}
          color="secondary"
          trend="up"
        />
        <KPICard
          title="Goal Progress"
          value={`${mockData.goalProgress}%`}
          change={5.1}
          icon={Target}
          color="accent"
          trend="up"
        />
        <KPICard
          title="Productivity Score"
          value={mockData.productivityScore}
          change={-2.3}
          icon={Activity}
          color="primary"
          trend="down"
        />
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Trajectory Hub */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="p-8" glow="blue">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold font-display">Trajectory Hub</h2>
              <Zap className="w-6 h-6 text-primary-500" />
            </div>

            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Wealth Building Progress</span>
                  <span className="text-sm text-muted-foreground">Target: $1M</span>
                </div>
                <ProgressBar value={mockData.netWorth} max={1000000} color="primary" />
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Annual Goals</span>
                  <span className="text-sm text-muted-foreground">{mockData.completedGoals}/{mockData.weeklyGoals * 13} completed</span>
                </div>
                <ProgressBar value={mockData.completedGoals} max={mockData.weeklyGoals * 13} color="secondary" />
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Daily Habits</span>
                  <span className="text-sm text-muted-foreground">{mockData.habitsCompleted}/{mockData.totalHabits} today</span>
                </div>
                <ProgressBar value={mockData.habitsCompleted} max={mockData.totalHabits} color="accent" />
              </div>
            </div>
          </Card>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="text-center" glow="magenta">
              <Calendar className="w-8 h-8 text-secondary-500 mx-auto mb-2" />
              <p className="text-2xl font-bold">{mockData.weeklyGoals}</p>
              <p className="text-sm text-muted-foreground">Weekly Goals</p>
            </Card>

            <Card className="text-center" glow="gold">
              <Award className="w-8 h-8 text-accent-500 mx-auto mb-2" />
              <p className="text-2xl font-bold">23</p>
              <p className="text-sm text-muted-foreground">Achievements</p>
            </Card>

            <Card className="text-center" glow="blue">
              <BarChart3 className="w-8 h-8 text-primary-500 mx-auto mb-2" />
              <p className="text-2xl font-bold">94%</p>
              <p className="text-sm text-muted-foreground">Success Rate</p>
            </Card>
          </div>
        </div>

        {/* Sidebar Widgets */}
        <div className="space-y-6">
          <Card glow="magenta">
            <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                <span className="text-sm">Completed morning routine</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-secondary-500 rounded-full"></div>
                <span className="text-sm">Updated investment portfolio</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-accent-500 rounded-full"></div>
                <span className="text-sm">Reached daily step goal</span>
              </div>
            </div>
          </Card>

          <Card glow="gold">
            <h3 className="text-lg font-semibold mb-4">Next Milestones</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">$150K Net Worth</span>
                <span className="text-xs text-muted-foreground">2 months</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Launch Side Business</span>
                <span className="text-xs text-muted-foreground">6 months</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">First $10K Month</span>
                <span className="text-xs text-muted-foreground">8 months</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
