"use client";

import DashboardLayout from "@/components/layout/DashboardLayout";
import KPICard from "@/components/ui/KPICard";
import Card from "@/components/ui/Card";
import ProgressBar from "@/components/ui/ProgressBar";
import StatCard from "@/components/ui/StatCard";
import LineChart from "@/components/charts/LineChart";
import AreaChart from "@/components/charts/AreaChart";
import HeatMap from "@/components/ui/HeatMap";
import FloatingActionButton from "@/components/ui/FloatingActionButton";
import {
  DollarSign,
  TrendingUp,
  Target,
  Activity,
  Calendar,
  Award,
  Zap,
  BarChart3,
  Flame,
  Clock,
  Plus
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";

// Mock data - in a real app, this would come from an API
const mockData = {
  netWorth: 125000,
  monthlyIncome: 8500,
  goalProgress: 68,
  habitsCompleted: 12,
  totalHabits: 18,
  weeklyGoals: 4,
  completedGoals: 3,
  productivityScore: 85
};

// Mock chart data
const wealthData = [
  { name: "Jan", value: 95000 },
  { name: "Feb", value: 102000 },
  { name: "<PERSON>", value: 108000 },
  { name: "Apr", value: 115000 },
  { name: "May", value: 120000 },
  { name: "Jun", value: 125000 },
];

const incomeData = [
  { name: "Jan", value: 7200 },
  { name: "Feb", value: 7800 },
  { name: "Mar", value: 8100 },
  { name: "Apr", value: 8300 },
  { name: "May", value: 8200 },
  { name: "Jun", value: 8500 },
];

// Mock habit tracking data
const habitData = Array.from({ length: 365 }, (_, i) => {
  const date = new Date();
  date.setDate(date.getDate() - (365 - i));
  return {
    date: date.toISOString().split('T')[0],
    value: Math.floor(Math.random() * 10),
    level: Math.floor(Math.random() * 5) as 0 | 1 | 2 | 3 | 4
  };
});

export default function Home() {
  return (
    <DashboardLayout>
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold font-display bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 bg-clip-text text-transparent mb-2">
          Welcome to Mission Control
        </h1>
        <p className="text-muted-foreground text-lg">
          Track your journey from zero to billion with precision and purpose.
        </p>
      </div>

      {/* KPI Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <KPICard
          title="Net Worth"
          value={formatCurrency(mockData.netWorth)}
          change={12.5}
          icon={DollarSign}
          color="primary"
          trend="up"
        />
        <KPICard
          title="Monthly Income"
          value={formatCurrency(mockData.monthlyIncome)}
          change={8.2}
          icon={TrendingUp}
          color="secondary"
          trend="up"
        />
        <KPICard
          title="Goal Progress"
          value={`${mockData.goalProgress}%`}
          change={5.1}
          icon={Target}
          color="accent"
          trend="up"
        />
        <KPICard
          title="Productivity Score"
          value={mockData.productivityScore}
          change={-2.3}
          icon={Activity}
          color="primary"
          trend="down"
        />
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Trajectory Hub */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="p-8" glow="blue">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold font-display">Trajectory Hub</h2>
              <Zap className="w-6 h-6 text-primary-500" />
            </div>

            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Wealth Building Progress</span>
                  <span className="text-sm text-muted-foreground">Target: $1M</span>
                </div>
                <ProgressBar value={mockData.netWorth} max={1000000} color="primary" />
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Annual Goals</span>
                  <span className="text-sm text-muted-foreground">{mockData.completedGoals}/{mockData.weeklyGoals * 13} completed</span>
                </div>
                <ProgressBar value={mockData.completedGoals} max={mockData.weeklyGoals * 13} color="secondary" />
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Daily Habits</span>
                  <span className="text-sm text-muted-foreground">{mockData.habitsCompleted}/{mockData.totalHabits} today</span>
                </div>
                <ProgressBar value={mockData.habitsCompleted} max={mockData.totalHabits} color="accent" />
              </div>
            </div>
          </Card>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <StatCard
              title="Weekly Goals"
              value={mockData.weeklyGoals}
              subtitle="This week"
              icon={Calendar}
              color="secondary"
            />

            <StatCard
              title="Achievements"
              value="23"
              subtitle="Total earned"
              icon={Award}
              color="accent"
            />

            <StatCard
              title="Success Rate"
              value="94%"
              subtitle="Last 30 days"
              icon={BarChart3}
              color="primary"
            />
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <Card glow="blue">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-primary-500" />
                Net Worth Growth
              </h3>
              <AreaChart data={wealthData} color="primary" height={250} />
            </Card>

            <Card glow="magenta">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <DollarSign className="w-5 h-5 mr-2 text-secondary-500" />
                Monthly Income
              </h3>
              <LineChart data={incomeData} color="secondary" height={250} />
            </Card>
          </div>
        </div>

        {/* Sidebar Widgets */}
        <div className="space-y-6">
          <Card glow="magenta">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Activity className="w-5 h-5 mr-2 text-secondary-500" />
              Recent Activity
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                <span className="text-sm">Completed morning routine</span>
                <span className="text-xs text-muted-foreground ml-auto">2h ago</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-secondary-500 rounded-full"></div>
                <span className="text-sm">Updated investment portfolio</span>
                <span className="text-xs text-muted-foreground ml-auto">4h ago</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-accent-500 rounded-full"></div>
                <span className="text-sm">Reached daily step goal</span>
                <span className="text-xs text-muted-foreground ml-auto">6h ago</span>
              </div>
            </div>
          </Card>

          <Card glow="gold">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2 text-accent-500" />
              Next Milestones
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">$150K Net Worth</span>
                <span className="text-xs text-muted-foreground">2 months</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Launch Side Business</span>
                <span className="text-xs text-muted-foreground">6 months</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">First $10K Month</span>
                <span className="text-xs text-muted-foreground">8 months</span>
              </div>
            </div>
          </Card>

          <Card glow="blue">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Flame className="w-5 h-5 mr-2 text-primary-500" />
              Habit Streak
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Current Streak</span>
                <span className="text-2xl font-bold text-primary-500">12 days</span>
              </div>
              <HeatMap
                data={habitData}
                color="primary"
                weeks={12}
                cellSize={10}
                className="mt-4"
              />
            </div>
          </Card>
        </div>
      </div>

      {/* Additional Dashboard Section */}
      <div className="mt-8">
        <Card glow="gold">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold font-display flex items-center">
              <Clock className="w-6 h-6 mr-2 text-accent-500" />
              Today's Focus
            </h2>
            <span className="text-sm text-muted-foreground">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h3 className="font-semibold text-primary-500">Priority Tasks</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <span className="text-sm">Review investment portfolio</span>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" defaultChecked />
                  <span className="text-sm line-through opacity-60">Complete morning workout</span>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <span className="text-sm">Update financial spreadsheet</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold text-secondary-500">Learning Goals</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" defaultChecked />
                  <span className="text-sm line-through opacity-60">Read 30 minutes</span>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <span className="text-sm">Complete online course module</span>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <span className="text-sm">Practice new skill</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold text-accent-500">Health & Wellness</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" defaultChecked />
                  <span className="text-sm line-through opacity-60">Drink 8 glasses of water</span>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <span className="text-sm">Meditate for 10 minutes</span>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" defaultChecked />
                  <span className="text-sm line-through opacity-60">Take vitamins</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Floating Action Button */}
      <FloatingActionButton
        icon={Plus}
        color="primary"
        tooltip="Quick Add"
        onClick={() => console.log("Quick add clicked")}
      />
    </DashboardLayout>
  );
}
