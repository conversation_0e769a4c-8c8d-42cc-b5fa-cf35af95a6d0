import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Dark theme base colors
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        
        // Brand colors with neon accents
        primary: {
          50: "#e6f9ff",
          100: "#b3f0ff",
          200: "#80e7ff",
          300: "#4dddff",
          400: "#1ad4ff",
          500: "#00d4ff", // Electric blue
          600: "#00a8cc",
          700: "#007d99",
          800: "#005266",
          900: "#002633",
        },
        
        secondary: {
          50: "#ffe6f5",
          100: "#ffb3e6",
          200: "#ff80d6",
          300: "#ff4dc7",
          400: "#ff1ab8",
          500: "#ff006b", // Vibrant magenta
          600: "#cc0055",
          700: "#990040",
          800: "#66002a",
          900: "#330015",
        },
        
        accent: {
          50: "#fffef0",
          100: "#fffbd1",
          200: "#fff8a3",
          300: "#fff574",
          400: "#fff246",
          500: "#ffd700", // Gold
          600: "#ccac00",
          700: "#998100",
          800: "#665600",
          900: "#332b00",
        },
        
        // Glassmorphism colors
        glass: {
          light: "rgba(255, 255, 255, 0.1)",
          medium: "rgba(255, 255, 255, 0.2)",
          dark: "rgba(0, 0, 0, 0.3)",
        },
        
        // Dark theme specific
        card: "hsl(var(--card))",
        "card-foreground": "hsl(var(--card-foreground))",
        popover: "hsl(var(--popover))",
        "popover-foreground": "hsl(var(--popover-foreground))",
        muted: "hsl(var(--muted))",
        "muted-foreground": "hsl(var(--muted-foreground))",
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
      },
      
      fontFamily: {
        sans: ["Inter", "system-ui", "sans-serif"],
        display: ["Montserrat", "Inter", "system-ui", "sans-serif"],
      },
      
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic": "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
        "glass-gradient": "linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))",
        "neon-gradient": "linear-gradient(135deg, #00d4ff, #ff006b, #ffd700)",
      },
      
      backdropBlur: {
        xs: "2px",
      },
      
      boxShadow: {
        "glass": "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
        "neon-blue": "0 0 20px rgba(0, 212, 255, 0.5)",
        "neon-magenta": "0 0 20px rgba(255, 0, 107, 0.5)",
        "neon-gold": "0 0 20px rgba(255, 215, 0, 0.5)",
      },
      
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
        "pulse-neon": "pulseNeon 2s ease-in-out infinite alternate",
        "float": "float 3s ease-in-out infinite",
      },
      
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        pulseNeon: {
          "0%": { boxShadow: "0 0 5px rgba(0, 212, 255, 0.5)" },
          "100%": { boxShadow: "0 0 20px rgba(0, 212, 255, 0.8)" },
        },
        float: {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-10px)" },
        },
      },
    },
  },
  plugins: [],
};

export default config;
