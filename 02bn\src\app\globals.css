@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Montserrat:wght@400;500;600;700;800;900&display=swap');

:root {
  /* Dark theme colors */
  --background: 220 13% 9%;
  --foreground: 220 9% 95%;
  --card: 220 13% 11%;
  --card-foreground: 220 9% 95%;
  --popover: 220 13% 11%;
  --popover-foreground: 220 9% 95%;
  --primary: 195 100% 50%;
  --primary-foreground: 220 13% 9%;
  --secondary: 220 13% 15%;
  --secondary-foreground: 220 9% 95%;
  --muted: 220 13% 15%;
  --muted-foreground: 220 9% 60%;
  --accent: 220 13% 15%;
  --accent-foreground: 220 9% 95%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 220 9% 95%;
  --border: 220 13% 20%;
  --input: 220 13% 20%;
  --ring: 195 100% 50%;
  --radius: 0.5rem;
}

* {
  border-color: hsl(var(--border));
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: 'Inter', system-ui, sans-serif;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
  overflow-x: hidden;
}

/* Glassmorphism utilities */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Neon glow effects */
.neon-blue {
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.neon-magenta {
  box-shadow: 0 0 20px rgba(255, 0, 107, 0.5);
}

.neon-gold {
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Mission control aesthetic */
.command-center {
  background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
}

.trajectory-grid {
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Loading animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}
