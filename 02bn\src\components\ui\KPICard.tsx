"use client";

import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import Card from "./Card";

interface KPICardProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon: LucideIcon;
  className?: string;
  color?: "primary" | "secondary" | "accent";
  trend?: "up" | "down" | "neutral";
}

export default function KPICard({
  title,
  value,
  change,
  changeLabel = "vs last month",
  icon: Icon,
  className,
  color = "primary",
  trend = "neutral"
}: KPICardProps) {
  const colorClasses = {
    primary: "text-primary-500 bg-primary-500/20",
    secondary: "text-secondary-500 bg-secondary-500/20",
    accent: "text-accent-500 bg-accent-500/20"
  };

  const trendClasses = {
    up: "text-green-500",
    down: "text-red-500", 
    neutral: "text-muted-foreground"
  };

  const trendSymbol = {
    up: "+",
    down: "-",
    neutral: ""
  };

  const glowMapping = {
    primary: "blue" as const,
    secondary: "magenta" as const,
    accent: "gold" as const
  };

  return (
    <Card className={cn("relative overflow-hidden", className)} glow={glowMapping[color]}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-muted-foreground mb-1">
            {title}
          </p>
          <motion.p
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="text-3xl font-bold font-display"
          >
            {value}
          </motion.p>
          
          {change !== undefined && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="flex items-center mt-2 text-sm"
            >
              <span className={cn("font-medium", trendClasses[trend])}>
                {trendSymbol[trend]}{Math.abs(change)}%
              </span>
              <span className="text-muted-foreground ml-1">
                {changeLabel}
              </span>
            </motion.div>
          )}
        </div>
        
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className={cn(
            "p-3 rounded-lg",
            colorClasses[color]
          )}
        >
          <Icon className="w-6 h-6" />
        </motion.div>
      </div>
      
      {/* Background decoration */}
      <div className="absolute -right-4 -bottom-4 w-24 h-24 opacity-5">
        <Icon className="w-full h-full" />
      </div>
    </Card>
  );
}
