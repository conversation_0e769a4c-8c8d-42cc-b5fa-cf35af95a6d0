"use client";

import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import Card from "./Card";

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: LucideIcon;
  className?: string;
  color?: "primary" | "secondary" | "accent";
  size?: "sm" | "md" | "lg";
}

export default function StatCard({
  title,
  value,
  subtitle,
  icon: Icon,
  className,
  color = "primary",
  size = "md"
}: StatCardProps) {
  const colorClasses = {
    primary: "text-primary-500 bg-primary-500/20",
    secondary: "text-secondary-500 bg-secondary-500/20",
    accent: "text-accent-500 bg-accent-500/20"
  };

  const sizeClasses = {
    sm: {
      icon: "w-4 h-4",
      iconContainer: "p-2",
      title: "text-xs",
      value: "text-lg",
      subtitle: "text-xs"
    },
    md: {
      icon: "w-6 h-6",
      iconContainer: "p-3",
      title: "text-sm",
      value: "text-2xl",
      subtitle: "text-sm"
    },
    lg: {
      icon: "w-8 h-8",
      iconContainer: "p-4",
      title: "text-base",
      value: "text-3xl",
      subtitle: "text-base"
    }
  };

  const glowMapping = {
    primary: "blue" as const,
    secondary: "magenta" as const,
    accent: "gold" as const
  };

  return (
    <Card className={cn("text-center", className)} glow={glowMapping[color]}>
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className={cn(
          "mx-auto rounded-lg mb-3",
          colorClasses[color],
          sizeClasses[size].iconContainer
        )}
      >
        <Icon className={cn(sizeClasses[size].icon)} />
      </motion.div>
      
      <motion.p
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className={cn(
          "font-medium text-muted-foreground mb-1",
          sizeClasses[size].title
        )}
      >
        {title}
      </motion.p>
      
      <motion.p
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.3 }}
        className={cn(
          "font-bold font-display",
          sizeClasses[size].value
        )}
      >
        {value}
      </motion.p>
      
      {subtitle && (
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className={cn(
            "text-muted-foreground mt-1",
            sizeClasses[size].subtitle
          )}
        >
          {subtitle}
        </motion.p>
      )}
    </Card>
  );
}
