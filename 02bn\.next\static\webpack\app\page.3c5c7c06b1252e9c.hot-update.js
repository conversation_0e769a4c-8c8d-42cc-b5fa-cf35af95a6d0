"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/plus.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Plus)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M5 12h14\",\n            key: \"1ays0h\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 5v14\",\n            key: \"s699le\"\n        }\n    ]\n];\nconst Plus = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"plus\", __iconNode);\n //# sourceMappingURL=plus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(app-pages-browser)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/KPICard */ \"(app-pages-browser)/./src/components/ui/KPICard.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ProgressBar */ \"(app-pages-browser)/./src/components/ui/ProgressBar.tsx\");\n/* harmony import */ var _components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/StatCard */ \"(app-pages-browser)/./src/components/ui/StatCard.tsx\");\n/* harmony import */ var _components_charts_LineChart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/charts/LineChart */ \"(app-pages-browser)/./src/components/charts/LineChart.tsx\");\n/* harmony import */ var _components_charts_AreaChart__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/charts/AreaChart */ \"(app-pages-browser)/./src/components/charts/AreaChart.tsx\");\n/* harmony import */ var _components_ui_HeatMap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/HeatMap */ \"(app-pages-browser)/./src/components/ui/HeatMap.tsx\");\n/* harmony import */ var _components_ui_FloatingActionButton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/FloatingActionButton */ \"(app-pages-browser)/./src/components/ui/FloatingActionButton.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n// Mock data - in a real app, this would come from an API\nconst mockData = {\n    netWorth: 125000,\n    monthlyIncome: 8500,\n    goalProgress: 68,\n    habitsCompleted: 12,\n    totalHabits: 18,\n    weeklyGoals: 4,\n    completedGoals: 3,\n    productivityScore: 85\n};\n// Mock chart data\nconst wealthData = [\n    {\n        name: \"Jan\",\n        value: 95000\n    },\n    {\n        name: \"Feb\",\n        value: 102000\n    },\n    {\n        name: \"Mar\",\n        value: 108000\n    },\n    {\n        name: \"Apr\",\n        value: 115000\n    },\n    {\n        name: \"May\",\n        value: 120000\n    },\n    {\n        name: \"Jun\",\n        value: 125000\n    }\n];\nconst incomeData = [\n    {\n        name: \"Jan\",\n        value: 7200\n    },\n    {\n        name: \"Feb\",\n        value: 7800\n    },\n    {\n        name: \"Mar\",\n        value: 8100\n    },\n    {\n        name: \"Apr\",\n        value: 8300\n    },\n    {\n        name: \"May\",\n        value: 8200\n    },\n    {\n        name: \"Jun\",\n        value: 8500\n    }\n];\n// Mock habit tracking data\nconst habitData = Array.from({\n    length: 365\n}, (_, i)=>{\n    const date = new Date();\n    date.setDate(date.getDate() - (365 - i));\n    return {\n        date: date.toISOString().split('T')[0],\n        value: Math.floor(Math.random() * 10),\n        level: Math.floor(Math.random() * 5)\n    };\n});\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold font-display bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 bg-clip-text text-transparent mb-2\",\n                        children: \"Welcome to Mission Control\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground text-lg\",\n                        children: \"Track your journey from zero to billion with precision and purpose.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Net Worth\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(mockData.netWorth),\n                        change: 12.5,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                        color: \"primary\",\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Monthly Income\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(mockData.monthlyIncome),\n                        change: 8.2,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                        color: \"secondary\",\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Goal Progress\",\n                        value: \"\".concat(mockData.goalProgress, \"%\"),\n                        change: 5.1,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                        color: \"accent\",\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Productivity Score\",\n                        value: mockData.productivityScore,\n                        change: -2.3,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                        color: \"primary\",\n                        trend: \"down\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"p-8\",\n                                glow: \"blue\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold font-display\",\n                                                children: \"Trajectory Hub\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-6 h-6 text-primary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Wealth Building Progress\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Target: $1M\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: mockData.netWorth,\n                                                        max: 1000000,\n                                                        color: \"primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Annual Goals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    mockData.completedGoals,\n                                                                    \"/\",\n                                                                    mockData.weeklyGoals * 13,\n                                                                    \" completed\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: mockData.completedGoals,\n                                                        max: mockData.weeklyGoals * 13,\n                                                        color: \"secondary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Daily Habits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    mockData.habitsCompleted,\n                                                                    \"/\",\n                                                                    mockData.totalHabits,\n                                                                    \" today\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: mockData.habitsCompleted,\n                                                        max: mockData.totalHabits,\n                                                        color: \"accent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Weekly Goals\",\n                                        value: mockData.weeklyGoals,\n                                        subtitle: \"This week\",\n                                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                        color: \"secondary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Achievements\",\n                                        value: \"23\",\n                                        subtitle: \"Total earned\",\n                                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        color: \"accent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Success Rate\",\n                                        value: \"94%\",\n                                        subtitle: \"Last 30 days\",\n                                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                        color: \"primary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        glow: \"blue\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-primary-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Net Worth Growth\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_AreaChart__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                data: wealthData,\n                                                color: \"primary\",\n                                                height: 250\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        glow: \"magenta\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-secondary-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Monthly Income\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_LineChart__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                data: incomeData,\n                                                color: \"secondary\",\n                                                height: 250\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                glow: \"magenta\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-secondary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recent Activity\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-primary-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Completed morning routine\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-auto\",\n                                                        children: \"2h ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-secondary-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Updated investment portfolio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-auto\",\n                                                        children: \"4h ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-accent-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Reached daily step goal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-auto\",\n                                                        children: \"6h ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                glow: \"gold\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-accent-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Next Milestones\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"$150K Net Worth\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"2 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Launch Side Business\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"6 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"First $10K Month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"8 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                glow: \"blue\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-primary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Habit Streak\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Current Streak\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-primary-500\",\n                                                        children: \"12 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_HeatMap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                data: habitData,\n                                                color: \"primary\",\n                                                weeks: 12,\n                                                cellSize: 10,\n                                                className: \"mt-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    glow: \"gold\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold font-display flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-6 h-6 mr-2 text-accent-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Today's Focus\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: new Date().toLocaleDateString('en-US', {\n                                        weekday: 'long',\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-primary-500\",\n                                            children: \"Priority Tasks\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Review investment portfolio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Complete morning workout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Update financial spreadsheet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-secondary-500\",\n                                            children: \"Learning Goals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Read 30 minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Complete online course module\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Practice new skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-accent-500\",\n                                            children: \"Health & Wellness\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Drink 8 glasses of water\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Meditate for 10 minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Take vitamins\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FloatingActionButton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                color: \"primary\",\n                tooltip: \"Quick Add\",\n                onClick: ()=>console.log(\"Quick add clicked\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/FloatingActionButton.tsx":
/*!****************************************************!*\
  !*** ./src/components/ui/FloatingActionButton.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FloatingActionButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction FloatingActionButton(param) {\n    let { icon: Icon, onClick, className, color = \"primary\", size = \"md\", tooltip } = param;\n    const colorClasses = {\n        primary: \"bg-primary-500 hover:bg-primary-600 shadow-primary-500/25\",\n        secondary: \"bg-secondary-500 hover:bg-secondary-600 shadow-secondary-500/25\",\n        accent: \"bg-accent-500 hover:bg-accent-600 shadow-accent-500/25\"\n    };\n    const sizeClasses = {\n        sm: \"w-12 h-12\",\n        md: \"w-14 h-14\",\n        lg: \"w-16 h-16\"\n    };\n    const iconSizes = {\n        sm: \"w-5 h-5\",\n        md: \"w-6 h-6\",\n        lg: \"w-7 h-7\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n        whileHover: {\n            scale: 1.1,\n            y: -2\n        },\n        whileTap: {\n            scale: 0.95\n        },\n        initial: {\n            scale: 0,\n            opacity: 0\n        },\n        animate: {\n            scale: 1,\n            opacity: 1\n        },\n        transition: {\n            type: \"spring\",\n            stiffness: 260,\n            damping: 20,\n            delay: 0.5\n        },\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"fixed bottom-6 right-6 rounded-full text-white shadow-lg transition-all duration-200 flex items-center justify-center z-50\", colorClasses[color], sizeClasses[size], className),\n        title: tooltip,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                className: iconSizes[size]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\FloatingActionButton.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute inset-0 rounded-full\",\n                initial: {\n                    scale: 0,\n                    opacity: 0.5\n                },\n                animate: {\n                    scale: 1.5,\n                    opacity: 0\n                },\n                transition: {\n                    duration: 1,\n                    repeat: Infinity\n                },\n                style: {\n                    background: \"radial-gradient(circle, \".concat(color === \"primary\" ? \"#00d4ff\" : color === \"secondary\" ? \"#ff006b\" : \"#ffd700\", \"40 0%, transparent 70%)\")\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\FloatingActionButton.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\ui\\\\FloatingActionButton.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_c = FloatingActionButton;\nvar _c;\n$RefreshReg$(_c, \"FloatingActionButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL0Zsb2F0aW5nQWN0aW9uQnV0dG9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUV1QztBQUVOO0FBV2xCLFNBQVNFLHFCQUFxQixLQU9qQjtRQVBpQixFQUMzQ0MsTUFBTUMsSUFBSSxFQUNWQyxPQUFPLEVBQ1BDLFNBQVMsRUFDVEMsUUFBUSxTQUFTLEVBQ2pCQyxPQUFPLElBQUksRUFDWEMsT0FBTyxFQUNtQixHQVBpQjtJQVEzQyxNQUFNQyxlQUFlO1FBQ25CQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsUUFBUTtJQUNWO0lBRUEsTUFBTUMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBLE1BQU1DLFlBQVk7UUFDaEJILElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxxQkFDRSw4REFBQ2pCLGlEQUFNQSxDQUFDbUIsTUFBTTtRQUNaQyxZQUFZO1lBQUVDLE9BQU87WUFBS0MsR0FBRyxDQUFDO1FBQUU7UUFDaENDLFVBQVU7WUFBRUYsT0FBTztRQUFLO1FBQ3hCRyxTQUFTO1lBQUVILE9BQU87WUFBR0ksU0FBUztRQUFFO1FBQ2hDQyxTQUFTO1lBQUVMLE9BQU87WUFBR0ksU0FBUztRQUFFO1FBQ2hDRSxZQUFZO1lBQ1ZDLE1BQU07WUFDTkMsV0FBVztZQUNYQyxTQUFTO1lBQ1RDLE9BQU87UUFDVDtRQUNBMUIsU0FBU0E7UUFDVEMsV0FBV0wsOENBQUVBLENBQ1gsOEhBQ0FTLFlBQVksQ0FBQ0gsTUFBTSxFQUNuQk8sV0FBVyxDQUFDTixLQUFLLEVBQ2pCRjtRQUVGMEIsT0FBT3ZCOzswQkFFUCw4REFBQ0w7Z0JBQUtFLFdBQVdZLFNBQVMsQ0FBQ1YsS0FBSzs7Ozs7OzBCQUdoQyw4REFBQ1IsaURBQU1BLENBQUNpQyxHQUFHO2dCQUNUM0IsV0FBVTtnQkFDVmtCLFNBQVM7b0JBQUVILE9BQU87b0JBQUdJLFNBQVM7Z0JBQUk7Z0JBQ2xDQyxTQUFTO29CQUFFTCxPQUFPO29CQUFLSSxTQUFTO2dCQUFFO2dCQUNsQ0UsWUFBWTtvQkFBRU8sVUFBVTtvQkFBR0MsUUFBUUM7Z0JBQVM7Z0JBQzVDQyxPQUFPO29CQUNMQyxZQUFZLDJCQUdYLE9BRkMvQixVQUFVLFlBQVksWUFDdEJBLFVBQVUsY0FBYyxZQUFZLFdBQ3JDO2dCQUNIOzs7Ozs7Ozs7Ozs7QUFJUjtLQWhFd0JMIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluLk5FSUxcXERlc2t0b3BcXDAyYm5cXDAyYm5cXHNyY1xcY29tcG9uZW50c1xcdWlcXEZsb2F0aW5nQWN0aW9uQnV0dG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSBcImZyYW1lci1tb3Rpb25cIjtcbmltcG9ydCB7IEx1Y2lkZUljb24gfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiO1xuXG5pbnRlcmZhY2UgRmxvYXRpbmdBY3Rpb25CdXR0b25Qcm9wcyB7XG4gIGljb246IEx1Y2lkZUljb247XG4gIG9uQ2xpY2s/OiAoKSA9PiB2b2lkO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIGNvbG9yPzogXCJwcmltYXJ5XCIgfCBcInNlY29uZGFyeVwiIHwgXCJhY2NlbnRcIjtcbiAgc2l6ZT86IFwic21cIiB8IFwibWRcIiB8IFwibGdcIjtcbiAgdG9vbHRpcD86IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRmxvYXRpbmdBY3Rpb25CdXR0b24oe1xuICBpY29uOiBJY29uLFxuICBvbkNsaWNrLFxuICBjbGFzc05hbWUsXG4gIGNvbG9yID0gXCJwcmltYXJ5XCIsXG4gIHNpemUgPSBcIm1kXCIsXG4gIHRvb2x0aXBcbn06IEZsb2F0aW5nQWN0aW9uQnV0dG9uUHJvcHMpIHtcbiAgY29uc3QgY29sb3JDbGFzc2VzID0ge1xuICAgIHByaW1hcnk6IFwiYmctcHJpbWFyeS01MDAgaG92ZXI6YmctcHJpbWFyeS02MDAgc2hhZG93LXByaW1hcnktNTAwLzI1XCIsXG4gICAgc2Vjb25kYXJ5OiBcImJnLXNlY29uZGFyeS01MDAgaG92ZXI6Ymctc2Vjb25kYXJ5LTYwMCBzaGFkb3ctc2Vjb25kYXJ5LTUwMC8yNVwiLFxuICAgIGFjY2VudDogXCJiZy1hY2NlbnQtNTAwIGhvdmVyOmJnLWFjY2VudC02MDAgc2hhZG93LWFjY2VudC01MDAvMjVcIlxuICB9O1xuXG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtOiBcInctMTIgaC0xMlwiLFxuICAgIG1kOiBcInctMTQgaC0xNFwiLFxuICAgIGxnOiBcInctMTYgaC0xNlwiXG4gIH07XG5cbiAgY29uc3QgaWNvblNpemVzID0ge1xuICAgIHNtOiBcInctNSBoLTVcIixcbiAgICBtZDogXCJ3LTYgaC02XCIsIFxuICAgIGxnOiBcInctNyBoLTdcIlxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPG1vdGlvbi5idXR0b25cbiAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMSwgeTogLTIgfX1cbiAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk1IH19XG4gICAgICBpbml0aWFsPXt7IHNjYWxlOiAwLCBvcGFjaXR5OiAwIH19XG4gICAgICBhbmltYXRlPXt7IHNjYWxlOiAxLCBvcGFjaXR5OiAxIH19XG4gICAgICB0cmFuc2l0aW9uPXt7IFxuICAgICAgICB0eXBlOiBcInNwcmluZ1wiLCBcbiAgICAgICAgc3RpZmZuZXNzOiAyNjAsIFxuICAgICAgICBkYW1waW5nOiAyMCxcbiAgICAgICAgZGVsYXk6IDAuNSBcbiAgICAgIH19XG4gICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJmaXhlZCBib3R0b20tNiByaWdodC02IHJvdW5kZWQtZnVsbCB0ZXh0LXdoaXRlIHNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgei01MFwiLFxuICAgICAgICBjb2xvckNsYXNzZXNbY29sb3JdLFxuICAgICAgICBzaXplQ2xhc3Nlc1tzaXplXSxcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgdGl0bGU9e3Rvb2x0aXB9XG4gICAgPlxuICAgICAgPEljb24gY2xhc3NOYW1lPXtpY29uU2l6ZXNbc2l6ZV19IC8+XG4gICAgICBcbiAgICAgIHsvKiBSaXBwbGUgZWZmZWN0ICovfVxuICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCByb3VuZGVkLWZ1bGxcIlxuICAgICAgICBpbml0aWFsPXt7IHNjYWxlOiAwLCBvcGFjaXR5OiAwLjUgfX1cbiAgICAgICAgYW5pbWF0ZT17eyBzY2FsZTogMS41LCBvcGFjaXR5OiAwIH19XG4gICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDEsIHJlcGVhdDogSW5maW5pdHkgfX1cbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSwgJHtcbiAgICAgICAgICAgIGNvbG9yID09PSBcInByaW1hcnlcIiA/IFwiIzAwZDRmZlwiIDpcbiAgICAgICAgICAgIGNvbG9yID09PSBcInNlY29uZGFyeVwiID8gXCIjZmYwMDZiXCIgOiBcIiNmZmQ3MDBcIlxuICAgICAgICAgIH00MCAwJSwgdHJhbnNwYXJlbnQgNzAlKWBcbiAgICAgICAgfX1cbiAgICAgIC8+XG4gICAgPC9tb3Rpb24uYnV0dG9uPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIm1vdGlvbiIsImNuIiwiRmxvYXRpbmdBY3Rpb25CdXR0b24iLCJpY29uIiwiSWNvbiIsIm9uQ2xpY2siLCJjbGFzc05hbWUiLCJjb2xvciIsInNpemUiLCJ0b29sdGlwIiwiY29sb3JDbGFzc2VzIiwicHJpbWFyeSIsInNlY29uZGFyeSIsImFjY2VudCIsInNpemVDbGFzc2VzIiwic20iLCJtZCIsImxnIiwiaWNvblNpemVzIiwiYnV0dG9uIiwid2hpbGVIb3ZlciIsInNjYWxlIiwieSIsIndoaWxlVGFwIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJhbmltYXRlIiwidHJhbnNpdGlvbiIsInR5cGUiLCJzdGlmZm5lc3MiLCJkYW1waW5nIiwiZGVsYXkiLCJ0aXRsZSIsImRpdiIsImR1cmF0aW9uIiwicmVwZWF0IiwiSW5maW5pdHkiLCJzdHlsZSIsImJhY2tncm91bmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/FloatingActionButton.tsx\n"));

/***/ })

});