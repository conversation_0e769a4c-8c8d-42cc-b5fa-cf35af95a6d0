"use client";

import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from "recharts";
import { cn } from "@/lib/utils";

interface DataPoint {
  name: string;
  value: number;
  [key: string]: string | number;
}

interface LineChartProps {
  data: DataPoint[];
  className?: string;
  color?: "primary" | "secondary" | "accent";
  height?: number;
  showGrid?: boolean;
  animated?: boolean;
}

export default function LineChart({
  data,
  className,
  color = "primary",
  height = 300,
  showGrid = true,
  animated = true
}: LineChartProps) {
  const colorMap = {
    primary: "#00d4ff",
    secondary: "#ff006b", 
    accent: "#ffd700"
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="glass-dark p-3 rounded-lg border border-border/50">
          <p className="text-sm font-medium">{label}</p>
          <p className="text-sm" style={{ color: colorMap[color] }}>
            Value: {payload[0].value.toLocaleString()}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <motion.div
      initial={animated ? { opacity: 0, y: 20 } : { opacity: 1 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={cn("w-full", className)}
      style={{ height }}
    >
      <ResponsiveContainer width="100%" height="100%">
        <RechartsLineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="rgba(255, 255, 255, 0.1)" 
            />
          )}
          <XAxis 
            dataKey="name" 
            axisLine={false}
            tickLine={false}
            tick={{ fill: "#9ca3af", fontSize: 12 }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fill: "#9ca3af", fontSize: 12 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Line
            type="monotone"
            dataKey="value"
            stroke={colorMap[color]}
            strokeWidth={3}
            dot={{ fill: colorMap[color], strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: colorMap[color], strokeWidth: 2 }}
            animationDuration={animated ? 1000 : 0}
          />
        </RechartsLineChart>
      </ResponsiveContainer>
    </motion.div>
  );
}
