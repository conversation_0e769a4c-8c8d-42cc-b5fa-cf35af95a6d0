"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  color?: "primary" | "secondary" | "accent";
  className?: string;
}

export default function LoadingSpinner({ 
  size = "md", 
  color = "primary",
  className 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8", 
    lg: "w-12 h-12"
  };

  const colorClasses = {
    primary: "border-primary-500",
    secondary: "border-secondary-500",
    accent: "border-accent-500"
  };

  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      className={cn(
        "border-2 border-transparent rounded-full",
        sizeClasses[size],
        className
      )}
      style={{
        borderTopColor: "currentColor",
        borderRightColor: "currentColor"
      }}
    />
  );
}
