"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(app-pages-browser)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/KPICard */ \"(app-pages-browser)/./src/components/ui/KPICard.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ProgressBar */ \"(app-pages-browser)/./src/components/ui/ProgressBar.tsx\");\n/* harmony import */ var _components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/StatCard */ \"(app-pages-browser)/./src/components/ui/StatCard.tsx\");\n/* harmony import */ var _components_charts_LineChart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/charts/LineChart */ \"(app-pages-browser)/./src/components/charts/LineChart.tsx\");\n/* harmony import */ var _components_charts_AreaChart__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/charts/AreaChart */ \"(app-pages-browser)/./src/components/charts/AreaChart.tsx\");\n/* harmony import */ var _components_ui_HeatMap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/HeatMap */ \"(app-pages-browser)/./src/components/ui/HeatMap.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Calendar,Clock,DollarSign,Flame,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n// Mock data - in a real app, this would come from an API\nconst mockData = {\n    netWorth: 125000,\n    monthlyIncome: 8500,\n    goalProgress: 68,\n    habitsCompleted: 12,\n    totalHabits: 18,\n    weeklyGoals: 4,\n    completedGoals: 3,\n    productivityScore: 85\n};\n// Mock chart data\nconst wealthData = [\n    {\n        name: \"Jan\",\n        value: 95000\n    },\n    {\n        name: \"Feb\",\n        value: 102000\n    },\n    {\n        name: \"Mar\",\n        value: 108000\n    },\n    {\n        name: \"Apr\",\n        value: 115000\n    },\n    {\n        name: \"May\",\n        value: 120000\n    },\n    {\n        name: \"Jun\",\n        value: 125000\n    }\n];\nconst incomeData = [\n    {\n        name: \"Jan\",\n        value: 7200\n    },\n    {\n        name: \"Feb\",\n        value: 7800\n    },\n    {\n        name: \"Mar\",\n        value: 8100\n    },\n    {\n        name: \"Apr\",\n        value: 8300\n    },\n    {\n        name: \"May\",\n        value: 8200\n    },\n    {\n        name: \"Jun\",\n        value: 8500\n    }\n];\n// Mock habit tracking data\nconst habitData = Array.from({\n    length: 365\n}, (_, i)=>{\n    const date = new Date();\n    date.setDate(date.getDate() - (365 - i));\n    return {\n        date: date.toISOString().split('T')[0],\n        value: Math.floor(Math.random() * 10),\n        level: Math.floor(Math.random() * 5)\n    };\n});\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold font-display bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 bg-clip-text text-transparent mb-2\",\n                        children: \"Welcome to Mission Control\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground text-lg\",\n                        children: \"Track your journey from zero to billion with precision and purpose.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Net Worth\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(mockData.netWorth),\n                        change: 12.5,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                        color: \"primary\",\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Monthly Income\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(mockData.monthlyIncome),\n                        change: 8.2,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                        color: \"secondary\",\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Goal Progress\",\n                        value: \"\".concat(mockData.goalProgress, \"%\"),\n                        change: 5.1,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                        color: \"accent\",\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_KPICard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: \"Productivity Score\",\n                        value: mockData.productivityScore,\n                        change: -2.3,\n                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                        color: \"primary\",\n                        trend: \"down\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"p-8\",\n                                glow: \"blue\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold font-display\",\n                                                children: \"Trajectory Hub\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-primary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Wealth Building Progress\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Target: $1M\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: mockData.netWorth,\n                                                        max: 1000000,\n                                                        color: \"primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Annual Goals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    mockData.completedGoals,\n                                                                    \"/\",\n                                                                    mockData.weeklyGoals * 13,\n                                                                    \" completed\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: mockData.completedGoals,\n                                                        max: mockData.weeklyGoals * 13,\n                                                        color: \"secondary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Daily Habits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    mockData.habitsCompleted,\n                                                                    \"/\",\n                                                                    mockData.totalHabits,\n                                                                    \" today\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: mockData.habitsCompleted,\n                                                        max: mockData.totalHabits,\n                                                        color: \"accent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Weekly Goals\",\n                                        value: mockData.weeklyGoals,\n                                        subtitle: \"This week\",\n                                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                        color: \"secondary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Achievements\",\n                                        value: \"23\",\n                                        subtitle: \"Total earned\",\n                                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                        color: \"accent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Success Rate\",\n                                        value: \"94%\",\n                                        subtitle: \"Last 30 days\",\n                                        icon: _barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        color: \"primary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        glow: \"blue\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-primary-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Net Worth Growth\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_AreaChart__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                data: wealthData,\n                                                color: \"primary\",\n                                                height: 250\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        glow: \"magenta\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-secondary-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Monthly Income\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_LineChart__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                data: incomeData,\n                                                color: \"secondary\",\n                                                height: 250\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                glow: \"magenta\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-secondary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recent Activity\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-primary-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Completed morning routine\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-auto\",\n                                                        children: \"2h ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-secondary-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Updated investment portfolio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-auto\",\n                                                        children: \"4h ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-accent-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Reached daily step goal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-auto\",\n                                                        children: \"6h ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                glow: \"gold\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-accent-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Next Milestones\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"$150K Net Worth\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"2 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Launch Side Business\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"6 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"First $10K Month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"8 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                glow: \"blue\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-primary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Habit Streak\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Current Streak\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-primary-500\",\n                                                        children: \"12 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_HeatMap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                data: habitData,\n                                                color: \"primary\",\n                                                weeks: 12,\n                                                cellSize: 10,\n                                                className: \"mt-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    glow: \"gold\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold font-display flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Calendar_Clock_DollarSign_Flame_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-6 h-6 mr-2 text-accent-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Today's Focus\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: new Date().toLocaleDateString('en-US', {\n                                        weekday: 'long',\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-primary-500\",\n                                            children: \"Priority Tasks\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Review investment portfolio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Complete morning workout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Update financial spreadsheet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-secondary-500\",\n                                            children: \"Learning Goals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Read 30 minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Complete online course module\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Practice new skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-accent-500\",\n                                            children: \"Health & Wellness\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Drink 8 glasses of water\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Meditate for 10 minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded\",\n                                                            defaultChecked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm line-through opacity-60\",\n                                                            children: \"Take vitamins\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});