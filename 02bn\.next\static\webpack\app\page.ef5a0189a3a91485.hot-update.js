"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,LayoutDashboard,Menu,Settings,Target,TrendingUp,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        current: true\n    },\n    {\n        name: \"Goals\",\n        href: \"/goals\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        current: false\n    },\n    {\n        name: \"Wealth\",\n        href: \"/wealth\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        current: false\n    },\n    {\n        name: \"Habits\",\n        href: \"/habits\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        current: false\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        current: false\n    }\n];\nfunction Sidebar(param) {\n    let { className } = param;\n    _s();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            x: -100,\n            opacity: 0\n        },\n        animate: {\n            x: 0,\n            opacity: 1\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-0 top-0 z-40 h-screen transition-all duration-300\", isCollapsed ? \"w-16\" : \"w-64\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass-dark h-full flex flex-col border-r border-border/50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-border/50\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-display font-bold text-xl bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent\",\n                                    children: \"02Bn\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsCollapsed(!isCollapsed),\n                            className: \"p-2 rounded-lg hover:bg-white/10 transition-colors\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_LayoutDashboard_Menu_Settings_Target_TrendingUp_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 p-4 space-y-2\",\n                    children: navigation.map((item)=>{\n                        const Icon = item.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.a, {\n                            href: item.href,\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            whileTap: {\n                                scale: 0.98\n                            },\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200\", item.current ? \"bg-primary-500/20 text-primary-400 border border-primary-500/30\" : \"text-muted-foreground hover:text-foreground hover:bg-white/5\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-5 h-5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 17\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.span, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        opacity: 1\n                                    },\n                                    className: \"font-medium\",\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-border/50\",\n                    children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Mission Control\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-500\",\n                                children: \"v1.0.0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\02bn\\\\02bn\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"XL80Ke9pMdZ2JRKLtHkkSCCoQZ0=\");\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Sidebar.tsx\n"));

/***/ })

});